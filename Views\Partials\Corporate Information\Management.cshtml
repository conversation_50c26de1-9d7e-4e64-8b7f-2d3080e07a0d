﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;
@{
    var managementPage = Model.DescendantsOfType("managementPage")?.FirstOrDefault() ?? null;
    var members = managementPage.Children.Where(x => x.IsVisible()).OrderBy(x => x.SortOrder).ToList();

    var maleIcon = "/media/hwwbbska/male.png";
    var femaleIcon = "/media/xtrnhmch/female.png";
    var closeIcon = "/media/gqeluogr/close.png";
    var pageName = managementPage.Name;
    bool maleSex = true;
}
<div class="management-container">
    @foreach (var item in members)
    {
        var personName = item.Value("personName").ToString();
        var position = item.Value("position").ToString();
        var personInformation = item.Value("personInformation").ToString();
        var sexIcon = "";
        var sex = item.Value("sex");
        if (sex.Equals("Female"))
        {
            sexIcon = femaleIcon;
        }else if(sex.Equals("Male")){
            sexIcon = maleIcon;
        }
        var convertName = Convert(pageName + "-" + personName);
        <div class="person" data-toggle="modal" data-target="@("#" + convertName)">
            <div class="info">
                <div class="name text-font-size-24">@personName</div>
                <div class="position text-font-size-16">@position</div>
            </div>
            <div class="sex-icon"><img src="@sexIcon" alt="Sex Icon"/></div>
        </div>
        <div id="@convertName" class="modal fade management-modal" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><img src="@closeIcon" alt="Close" /></button>
                        <div class="head-info">
                            <div class="name text-font-size-24">@personName</div>
                            <div class="position text-font-size-16">@position</div>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div class="information">
                            <div class="info">
                                <div class="desc">@Html.Raw(personInformation)</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>
@functions {
    public static string Convert(string name)
    {
        // string str = "";
        //equipmentName = Regex.Replace(equipmentName, @"[^A-Za-z0-9_\.~]+", "-");
        name = name.Replace(" ", "-");
        name = name.Replace("!", "");
        name = name.Replace("@", "");
        name = name.Replace("#", "");
        name = name.Replace("$", "");
        name = name.Replace("%", "");
        name = name.Replace("^", "");
        name = name.Replace("&", "");
        name = name.Replace("*", "");
        name = name.Replace("(", "");
        name = name.Replace(")", "");
        name = name.Replace("=", "");
        name = name.Replace("+", "");
        name = name.Replace("|", "");
        name = name.Replace("/", "");
        name = name.Replace("?", "");
        name = name.Replace(">", "");
        name = name.Replace("<", "");
        name = name.Replace(".", "");
        name = name.Replace(",", "");
        return name.ToLower();
    }
}