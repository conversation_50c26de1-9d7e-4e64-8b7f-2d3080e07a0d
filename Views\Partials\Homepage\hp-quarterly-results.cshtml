@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;

@{
    var quarterlyResultsTitle = Model.Value("quarterlyResultsTitle");
    var quaterlyBackground = "";
    List<IPublishedContent> quarterlyResultsPageContent = new List<IPublishedContent>();
    if (Model.HasValue("quaterlyBackground") && Model.Value<IPublishedContent>("quaterlyBackground") != null)
    {
        quaterlyBackground = Model.Value<IPublishedContent>("quaterlyBackground").Url();
    }
    var quarterlyResultsItem = Model.Value<IEnumerable<IPublishedElement>>("quarterlyResultsItem").ToList();

    var quarterlyResultsPage = Model.DescendantsOfType("quarterlyResultsPage")?.FirstOrDefault() ?? null;


    if (quarterlyResultsPage != null)
    {
        quarterlyResultsPageContent = quarterlyResultsPage.Children().OrderByDescending(x => x.Value("year")).ToList();
    }
    var firstItem = quarterlyResultsPageContent.FirstOrDefault();
    if (quarterlyResultsPageContent.Count() >= 1)
    {
        firstItem = quarterlyResultsPageContent.First();
    }

    var audioQuaterly = "";
    if (Model.HasValue("audioQuaterly") && Model.Value<IPublishedContent>("audioQuaterly") != null)
    {
        audioQuaterly = Model.Value<IPublishedContent>("audioQuaterly").Url();
    }

    var types = Model.Value("type");
    var year = Model.Value("year");
}

<div class="quarterly-background" style="background-image: url('@quaterlyBackground')">
    <div class="container" data-aos="fade-up" data-aos-duration="1000">
        <h3 class="title">@quarterlyResultsTitle</h3>
        <div class="box-quaterly-details">
            <div class="q1-info">
                @if (firstItem != null)
                {
                    @* var year = firstItem.Value("year").ToString(); *@
                    //Q1 Content
                    var q1PressReleasesFile = firstItem.Value<IPublishedContent>("q1PressReleasesFile")?.Url() ??
                    "javascript:void(0);";
                    var q1WebcastsFile = firstItem.Value<IPublishedContent>("q1WebcastFile")?.Url() ??
                    "javascript:void(0);";
                    var q1SECFilingFile = firstItem.Value<IPublishedContent>("q1SECFilingFile")?.Url() ??
                    "javascript:void(0);";
                    var q1HKEXFilingFile = firstItem.Value<IPublishedContent>("q1HKEXFilingFile")?.Url() ??
                    "javascript:void(0);";

                    //Q2 Content
                    var q2PressReleasesFile = firstItem.Value<IPublishedContent>("q2PressReleasesFile")?.Url() ??
                    "javascript:void(0);";
                    var q2WebcastsFile = firstItem.Value<IPublishedContent>("q2WebcastFile")?.Url() ??
                    "javascript:void(0);";
                    var q2SECFilingFile = firstItem.Value<IPublishedContent>("q2SECFilingFile")?.Url() ??
                    "javascript:void(0);";
                    var q2HKEXFilingFile = firstItem.Value<IPublishedContent>("q2HKEXFilingFile")?.Url() ??
                    "javascript:void(0);";

                    //Q3 Content
                    var q3PressReleasesFile = firstItem.Value<IPublishedContent>("q3PressReleasesFile")?.Url() ??
                    "javascript:void(0);";
                    var q3WebcastsFile = firstItem.Value<IPublishedContent>("q3WebcastFile")?.Url() ??
                    "javascript:void(0);";
                    var q3SECFilingFile = firstItem.Value<IPublishedContent>("q3SECFilingFile")?.Url() ??
                    "javascript:void(0);";
                    var q3HKEXFilingFile = firstItem.Value<IPublishedContent>("q3HKEXFilingFile")?.Url() ??
                    "javascript:void(0);";

                    //Q4 Content
                    var q4PressReleasesFile = firstItem.Value<IPublishedContent>("q4PressReleasesFile")?.Url() ??
                    "javascript:void(0);";
                    var q4WebcastsFile = firstItem.Value<IPublishedContent>("q4WebcastFile")?.Url() ??
                    "javascript:void(0);";
                    var q4SECFilingFile = firstItem.Value<IPublishedContent>("q4SECFilingFile")?.Url() ??
                    "javascript:void(0);";
                    var q4HKEXFilingFile = firstItem.Value<IPublishedContent>("q4HKEXFilingFile")?.Url() ??
                    "javascript:void(0);";
                    
                    <p class="text-font-size-40">@year</p>
                    @* <p class="text-font-size-64">@Umbraco.GetDictionaryValue("Q2")</p> *@
                    <p class="text-font-size-64">@types</p>
                }


            </div>

            <div class="list-quarterly-results">
                @if (quarterlyResultsItem != null && quarterlyResultsItem.Any())
                {
                    foreach (var item in quarterlyResultsItem)
                    {
                        var title = item.Value("title");
                        var photo = "";
                        if (item.HasValue("photo") && item.Value<IPublishedContent>("photo") != null)
                        {
                            photo = item.Value<IPublishedContent>("photo").Url();
                        }
                        var link = (item.HasValue("linkUrl") && item.Value<Link>("linkUrl") != null) ?
                        item.Value<Link>("linkUrl").Url : "";

                        <div class="icon-item">
                            <a class="link" href="@link" target="_blank">
                                <div class="wrap-img circle-wrap">
                                    <div class="circle">
                                        <div class="mask full">
                                            <div class="fill"></div>
                                        </div>
                                        <div class="mask half">
                                            <div class="fill"></div>
                                        </div>
                                        <div class="inside-circle">
                                            <img src="@photo" class="icon" alt="@title" />
                                            @* <audio id="myAudio" src="" preload="auto">
                                            </audio>
                                            <a class="play-audio" style="display: none;">Click here to hear.</a> *@
                                        </div>
                                    </div>
                                </div>
                                <div class="text-title text-font-size-24">@title</div>
                            </a>
                        </div>
                    }
                }
            </div>

            
        </div>


    </div>
</div>