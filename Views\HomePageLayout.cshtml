﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@using Umbraco.Cms.Core.WebAssets;

@{
    Layout = "ChildPageFluidLayout.cshtml";
    var home = Model.Root();
}

<section class="homepage-layout">

    <section class="hp__business-highlights" id="business-highlights" data-aos="fade-up" data-aos-duration="1000">
        @{
            Html.RenderPartial("~/Views/Partials/HomePage/hp-business-highlights.cshtml");
        }
    </section>

    <section class="hp__quarterly-results">
        @{
            Html.RenderPartial("~/Views/Partials/HomePage/hp-quarterly-results.cshtml");
        }
    </section>

    <section class="hp__recent-news" data-aos="fade-up" data-aos-duration="1000">
        @{
            Html.RenderPartial("~/Views/Partials/HomePage/hp-recent-news.cshtml");
        }
    </section>

    <section class="hp__esg" data-aos="fade-up" data-aos-duration="1000">
        @{
            Html.RenderPartial("~/Views/Partials/HomePage/hp-esg.cshtml");
        }
    </section>

    <section class="hp__quicklinks">
        @{
            Html.RenderPartial("~/Views/Partials/HomePage/hp-quicklinks.cshtml");
        }
    </section>
</section>