﻿using System;
//using System.Web.Mvc;
using System.IO;
//using Umbraco.Web.Mvc;
using System.Text.RegularExpressions;
//using umbraco.IO;
using System.Net.Mail;
using System.Configuration;
//using Umbraco.Core.IO;
using System.Net;
using umb_bilibili;

/// <summary>
/// Summary description for CMSVersionController
/// </summary>
namespace CMSCommon.Controllers
{
    
    public class CMSCommon
    {
        public static string defaultReceiveEmail = "<EMAIL>";
        public static List<News> GetNewsFromJson(string jsonUrl)
        {
            List<News> jsonReults = umb_bilibili.Json.Deserialize(jsonUrl);
            return jsonReults;
        }
       
        public static bool SendErrorEmail(string subject, string bodyMessage)
        {
            try
            {
                var receiveEmails = defaultReceiveEmail;
                //if (ConfigurationManager.AppSettings["ErrorReceiveEmails"] != null && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["ErrorReceiveEmails"]))
                //{
                //    receiveEmails = ConfigurationManager.AppSettings["ErrorReceiveEmails"].ToString();
                //}
                //else
                //{
                //    receiveEmails = defaultReceiveEmail;
                //}
                //from email in config file
                //string defaultFromEmail = "<EMAIL>";
                //support send multi separate email
                foreach (var _email in receiveEmails.Split(';'))
                {
                    try
                    {
                        SendAnEmail(_email, subject, bodyMessage);
                    }
                    catch (Exception ex)
                    {
                        //Error: Transaction failed. The server response was: 5.7.1 <<EMAIL>>: Relay access denied
                        if (ex.ToString().Contains("Relay access denied"))
                        {
                            SendAnEmail(defaultReceiveEmail, "Error - website - Relay access denied", ex.ToString());
                        }
                    }
                }
                return true;
            }
            catch
            {
                return false;
            }
        }
        /// <summary>
        /// Send an email, from value is in the config file
        /// </summary>
        /// <param name="toEmail"></param>
        /// <param name="subject"></param>
        /// <param name="bodyMessage"></param>
        public static void SendAnEmail(string toEmail, string subject, string bodyMessage)
        {
            MailMessage mail = new MailMessage();
            //mail.From = new MailAddress(defaultFromEmail);
            mail.To.Add(new MailAddress(toEmail, "Admin"));
            mail.IsBodyHtml = true;
            var language = System.Threading.Thread.CurrentThread.CurrentCulture.Name;
            if (language.Equals("ar-SA"))
            {
                mail.BodyEncoding = System.Text.Encoding.GetEncoding("iso-8859-1");
            }
            else
            {
                mail.BodyEncoding = System.Text.UTF8Encoding.UTF8;
            }
            SmtpClient SmtpServer = new SmtpClient();
            mail.Subject = subject;
            mail.Body = bodyMessage;
            SmtpServer.Send(mail);
        }
        public static bool CaptchaValidate(string recaptchaResponse)
        {
            //string Response = Request["g-recaptcha-response"];//Getting Response String Append to Post Method
            bool Valid = false;
            //Request to Google Server
            string secretKey = "";// ConfigurationManager.AppSettings["secretKey"].ToString();// "6LeJKNoZAAAAAN6EHvBMHA3mke4EoKtKspFiAPpO"; //"6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe";
            HttpWebRequest req = (HttpWebRequest)WebRequest.Create(string.Format("https://www.recaptcha.net/recaptcha/api/siteverify?secret={0}&response={1}", secretKey, recaptchaResponse));
            try
            {
                //Google recaptcha Response
                using (WebResponse wResponse = req.GetResponse())
                {

                    using (StreamReader readStream = new StreamReader(wResponse.GetResponseStream()))
                    {
                        string jsonResponse = readStream.ReadToEnd();

                        //JavaScriptSerializer js = new JavaScriptSerializer();
                        //RecaptchaResult data = js.Deserialize<RecaptchaResult>(jsonResponse);// Deserialize Json

                        //Valid = Convert.ToBoolean(data.success);
                    }
                }

                return Valid;
            }
            catch (WebException ex)
            {
                throw ex;
            }
        }
     
    }
  
   
    public class RecaptchaResult
    {
        public RecaptchaResult()
        {
            //
            // TODO: Add constructor logic here
            //
        }
        public string success { get; set; }
    }
}