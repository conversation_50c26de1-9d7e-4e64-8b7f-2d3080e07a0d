variables:
  AZURE_PROFILE: 'AzureProfile'
  AZURE_PROFILE_PATH: '.\Properties\PublishProfiles\$AZURE_PROFILE.pubxml'
  UMBRACO_ROOT: '\\bingo\umbraco_wwwroot\'
  
stages:
  - deploy

deploy_job_bingo:
  stage: deploy
  tags:
    - vietnam-dev-shell
  only:
    - master
  before_script:
    - |
      ECHO Verifying existing deployment folder in BINGO...
      IF NOT EXIST %UMBRACO_ROOT%%CI_PROJECT_NAME% (ECHO Could not found deployment folder at %UMBRACO_ROOT%%CI_PROJECT_NAME% & EXIT /B 1)
  script:
    - |
      ECHO Publishing website to BINGO...
      %windir%\system32\inetsrv\appcmd stop site %CI_PROJECT_NAME%
      DOTNET publish -o "%UMBRACO_ROOT%%CI_PROJECT_NAME%"
      %windir%\system32\inetsrv\appcmd start site %CI_PROJECT_NAME%
  environment:
    name: dev

deploy_job_azure:
  stage: deploy
  tags:
    - vietnam-dev-shell
  only:
    - azure
  before_script:
    - |
      ECHO Verifying publish profile...
      IF NOT EXIST %AZURE_PROFILE_PATH% (ECHO Could not found publish profile at %AZURE_PROFILE_PATH% & EXIT /B 1)
      ECHO Restore Nuget packages...
      dotnet restore
  script:
    - |
      ECHO Publishing website to Azure...
      dotnet msbuild /p:DeployOnBuild=true /p:PublishProfile="%AZURE_PROFILE%" /p:Username=%AZURE_DEPLOY_USER% /p:Password=%AZURE_DEPLOY_PWD%
  environment:
    name: live

