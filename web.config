﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" arguments=".\umb-bilibili.dll" stdoutLogEnabled="false" stdoutLogFile="\\?\%home%\LogFiles\stdout" hostingModel="inprocess" />
    </system.webServer>
  </location>
  <system.webServer>
    <httpProtocol>
      <customHeaders>
        <remove name="X-Powered-By" />
        <add name="X-Frame-Options" value="SAMEORIGIN" />
        <add name="X-XSS-Protection" value="1; mode=block" />
        <add name="X-Permitted-Cross-Domain-Policies" value="none" />
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="Content-Security-Policy" value="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.virtualearth.net *.addtoany.com *.bing.com *.eurolandir.com *.euroland.com *.google-analytics.com *.googletagmanager.com; style-src 'self' 'unsafe-inline' *.bing.com https://fonts.googleapis.com; frame-src 'self' https://static.addtoany.com *.addtoany.com *.euroland.com *.eurolandir.com https://www.google.com https://marketplace.umbraco.com; font-src 'self' https://fonts.gstatic.com data:; img-src 'self' data: *.virtualearth.net *.bing.com *.euroland.com *.eurolandir.com https://www.gravatar.com https://umbraco.tv *.umbraco.com *.umbraco.org *.googletagmanager.com *.google-analytics.com *.google.com *.github.com github.com; connect-src 'self' data: *.virtualearth.net *.bing.com *.eurolandir.com *.euroland.com *.google-analytics.com *.umbraco.org *.umbraco.com; media-src 'self' https://player.vimeo.com; worker-src blob:;frame-ancestors 'self'; form-action 'none'" />
        <!--Strict Transport Security Header-->
        <add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains" />
        <add name="Cache-Control" value="public, max-age=86400" />
      </customHeaders>
    </httpProtocol>
    <security>
      <requestFiltering removeServerHeader="true">
        <requestLimits maxAllowedContentLength="100000002" />
      </requestFiltering>
    </security>
    <rewrite>
      <rules>
        <rule name="Block Query String from Bot" stopProcessing="true">
          <match url="^$" />
          <conditions>
			<add input="{QUERY_STRING}" pattern=".+"
				 negate="false" /> <!-- This ensures there's a query string -->
		  </conditions>
          <action type="CustomResponse" statusCode="404" statusReason="Not Found" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>
<!--ProjectGuid: 601CA9F8-293F-44DF-80E3-0E3EDD58C4A9-->