.hp__esg {
    .esg-container {
        background-size: cover;
        background-repeat: no-repeat;
        background-position: right;

        @media (min-width: 200px) and (max-width: 1199px) {
            background-position: left;
        }

        @media (min-width: 1200px) {
            background-position: 50% 100%;
        }

        //background-position: right
    }

    .container {
        padding: 10rem 0;

        @media (max-width: 600px) {
            padding: 6rem 0 8rem 0;
        }
    }

    .esg__wrapper {
        padding: 2rem 0rem;
        @media (min-width: 768px) and (max-width: 1200px) {
            //background-color: rgba(12, 182, 242, 0.6);
            //padding: 2rem 7.7rem;
        }

        .esg__title {
            color: #fff;
            margin-bottom: 6rem;

            @media (max-width: 600px) {
                margin-bottom: 2rem;
            }
        }

        .esg__content {
            p {
                font-style: normal;
                font-weight: 400;
                // font-size: 2.4rem;
                line-height: 1;
                color: #ffffff;
            }

            ul li {
                @media (max-width: 600px) {
                    font-size: 1.4rem !important;
                }
            }

            > p {
                @media (max-width: 600px) {
                    font-size: 1.8rem;
                }
            }

            ul {
                list-style: disc;
                margin-top: 4rem;

                li {
                    font-style: normal;
                    font-weight: 400;
                    font-size: 2rem;
                    line-height: 1.5;
                    color: #ffffff;
                    margin-left: 2rem;

                    @media screen and (max-width: 991px) {
                        font-size: 1.8rem;
                    }
                }

                @media (max-width: 600px) {
                    margin-top: 2rem;
                }
            }
        }

        .box-content {
            max-width: 100%;

            @media (min-width: 1280px) and (max-width: 1399px) {
                max-width: 50%;
            }

            @media (min-width: 1400px) {
                max-width: 40%;
            }
        }

        .esg__sign {
            margin-top: 6rem;
            //max-width: 50%;

            p {
                font-size: 1.8rem;
                font-style: normal;
                font-weight: 400;
                // font-size: 2.4rem;
                text-align: right;
                color: #ffffff;

                &:last-child {
                    margin-top: 1rem;
                    font-style: normal;
                    font-weight: 400;
                    font-size: 1.6rem;
                    line-height: 1;
                    text-align: right;
                }
            }

            @media (max-width: 600px) {
                margin-top: 2rem;
            }
        }
    }

    @media screen and (max-width: 1200px) {
        .container {
            max-width: 100%;
        }

        .esg__wrapper {
            //background-color: rgba(12, 182, 242, 0.6);
            width: 100%;
            padding: 2rem 1.5rem;
            // border-top-right-radius: 2rem;
            // border-bottom-right-radius: 2rem;

            .esg__sign {
                max-width: 100%;
            }

            @media (min-width: 768px) and (max-width: 1200px) {
                padding: 2rem 7.7rem;
            }
        }
    }
}

:lang(cn),
:lang(hk) {
    .hp__recent-news {
        .news-box-content {
            .list-news {
                .news-item {
                    .title {
                        a {
                            // font-family: "Sofia Pro";
                            font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
                        }
                    }
                }
            }
        }
    }
}

.hp__recent-news {
    .container {
        padding: 18rem 0;

        @media screen and (max-width: 767px) {
            padding: 9rem 1.5rem;
        }

        @media (min-width: 1025px) and (max-width: 1500px) {
            padding: 8rem 0;
        }

        @media (min-width: 768px) and (max-width: 1024px) {
            padding: 12rem 0;
        }
    }

    .recent-news-title {
        text-align: center;
        color: $colorBlack;
        margin-bottom: 12rem;
        @media (min-width: 1025px) and (max-width: 1500px) {
            margin-bottom: 7rem;
        }

        @media screen and (max-width: 1200px) {
            margin-bottom: 10rem;
        }

        @media screen and (max-width: 600px) {
            margin-bottom: 2.5rem;
        }
    }

    .news-box-content {
        display: flex;
        align-items: stretch;
        justify-content: center;
        gap: 4rem;
        margin-bottom: 8rem;

        @media screen and (max-width: 1200px) {
            display: block;
            margin-bottom: 5rem;
        }
        @media screen and (max-width: 600px) {
            margin-bottom: 0;
        }

        .photo {
            width: 50%;

            @media screen and (max-width: 1200px) {
                margin-bottom: 4rem;
                text-align: center;
                width: 100%;
            }
            @media screen and (max-width: 600px) {
                margin-bottom: 2rem;
            }

            img {
                border-radius: 2rem;
                width: 100%;
                max-width: 68rem;
                height: 100%;
                object-fit: cover;
            }
        }

        .list-news {
            width: 50%;
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            @media screen and (max-width: 1200px) {
                text-align: center;
                width: 100%;
                align-items: center;
            }

            .news-item {
                border-bottom: 1px solid rgba(13, 28, 33, 0.12);
                max-width: 68rem;
                flex: 1;
                width: 100%;
                padding: 2rem 0;

                &:first-child {
                    padding-top: 0;
                }

                .title {
                    margin-bottom: 0.5rem;
                    text-align: left;

                    a {
                        font-weight: 400;

                        line-height: 1;
                        color: $colorBlack;
                        cursor: pointer;

                        &:hover {
                            color: $colorBlue;
                        }

                        @media (min-width: 768px) and (max-width: 1024px) {
                            font-weight: 700;
                            font-size: 1.6rem;
                        }

                        @media screen and (max-width: 500px) {
                            font-size: 1.8rem;
                        }
                    }
                }

                .published-date {
                    text-align: left;
                    font-weight: 400;
                    // font-size: 1.4rem;
                    line-height: 1;
                    color: $colorBlue;
                    // margin-bottom: 2rem;
                }

                @media (max-width: 600px) {
                    &:last-child {
                        border-bottom: none;
                    }
                }
            }
        }
    }

    .btn-view-more {
        text-align: center;

        a {
            width: 15rem;
            height: 5rem;
            background: $colorPink;
            border-radius: 3rem;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 400;
            font-size: 1.8rem;
            line-height: 2rem;
            color: #ffffff;
            margin: 0 auto;
            @include transition();
            @media (min-width: 767px) {
                font-size: 2rem;
            }

            &:hover {
                color: #fff;
                background: rgba(255, 85, 140, 0.8);
            }

            @media (max-width: 600px) {
                width: 10rem;
                height: 3.4rem;
                font-size: 1.4rem;
            }
        }
    }
}

//business-highlights
.hp__business-highlights {
    padding-top: 12rem;
    padding-bottom: 7rem;
    overflow-x: hidden;
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */

    &::-webkit-scrollbar {
        display: none;
    }

    h3.title {
        text-align: center;
    }

    .note-type {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    p.note-business {
        text-align: center;
        color: $colorBlue;
        font-size: 1.6rem;
        font-weight: 400;
        margin-top: 2rem;
        // font-family: "Sofia Pro";
        font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
    }

    .list-business-highlights {
        display: flex;
        flex-wrap: wrap;
        margin-top: 5rem;

        .item {
            width: calc(100% / 2);
            margin-bottom: 1.5rem;
            align-items: center;
            display: flex;
            flex-direction: column;

            &.desktop {
                display: none;
            }

            @media (min-width: 1200px) {
                width: calc(100% / 4);
                margin-bottom: 7rem;
                &.desktop {
                    display: block;
                }
                &.mobile {
                    display: none;
                }
            }

            .box-count {
                display: flex;
                justify-content: center;
                align-items: baseline;

                .unit-count {
                    font-size: 3.5rem;
                    line-height: 1;
                    font-weight: bold;
                    color: $colorPink;
                    padding-left: 1rem;

                    @media (max-width: 991px) {
                        font-size: 2.4rem;
                    }
                }

                .number-count {
                    margin-bottom: 1rem;
                    // font-size: 5rem;
                    font-weight: bold;
                    line-height: 1;
                    color: $colorPink;
                    // font-family: "Sofia Pro";
                    font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";

                    // @media (min-width: 768px) and (max-width: 991px) {
                    //     font-size: 6rem;
                    //     line-height: 6rem;
                    // }

                    // @media (min-width: 992px) {
                    //     font-size: 6.4rem;
                    //     line-height: 6.4rem;
                    // }

                    // @media (max-width: 600px) {
                    //     font-size: 4rem;
                    //     height: 4rem;
                    // }
                }
            }

            .content-count {
                height: 4rem;

                @media (min-width: 768px) {
                    height: auto;
                }
                p {
                    font-weight: 400;
                    // font-size: 1.9rem;
                    line-height: 1.1;
                    color: #0d1c21;
                    text-align: center;
                    @media (min-width: 200px) and (max-width: 767px) {
                        line-height: 1.4 !important;
                        font-size: 1.6rem !important;
                    }
                }
            }
        }
    }

    @media (min-width: 1200px) and (max-width: 1600px) {
        padding-top: 0;
    }

    @media (max-width: 600px) {
        padding-top: 8rem;
        padding-bottom: 7rem;
        overflow: hidden;
    }

    @media (min-width: 1025px) and (max-width: 1400px) {
        padding-top: 0 !important;
    }
}

html[lang="cn"],
html[lang="hk"] {
    .hp__business-highlights {
        .list-business-highlights {
            .item {
                .box-count {
                    .unit-count {
                        font-family: "Microsoft YaHei UI", "Microsoft YaHei-Regular";
                    }
                }
            }
        }
    }
}

//quarterly-results
.hp__quarterly-results {
    .quarterly-background {
        min-height: 57rem;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: bottom;
        background-attachment: fixed;

        @media (min-width: 200px) and (max-width: 767px) {
            background-attachment: unset;
            background-position: bottom center;
        }

        @media (min-width: 768px) and (max-width: 1000px) {
            min-height: 60rem;
        }

        @media (min-width: 1001px) {
            min-height: 65rem;
        }

        @media (max-width: 600px) {
            min-height: 51rem;
        }
    }

    h3.title {
        color: #ffffff;
        text-align: center;
        padding-top: 7rem;
        @media (min-width: 768px) {
            padding-top: 12rem;
        }

        @media (max-width: 600px) {
            padding-top: 8rem;
        }
    }

    .box-quaterly-details {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        //min-height: 40rem;

        @media (min-width: 768px) {
            min-height: 40rem;
        }

        .q1-info {
            width: calc(100%);
            margin-right: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            border-right: 0;
            padding: 1.5rem 0;
            // font-family: "Sofia Pro";
            font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";

            @media (min-width: 768px) and (max-width: 1024px) {
                flex-direction: unset;
                align-items: unset;
                justify-content: center;
                p {
                    font-size: 4rem;
                }
            }

            p {
                color: #ffffff;

                @media (min-width: 320px) and (max-width: 991px) {
                    font-size: 2.8rem;
                }

                &:first-child {
                    // font-size: 6.4rem;
                    line-height: 1;
                    font-weight: bold;
                    padding-bottom: 2rem;
                    @media (min-width: 768px) and (max-width: 1024px) {
                        padding-bottom: 0;
                    }

                    // @media screen and (min-width: 1024px) and (max-width: 1470px) {
                    //     font-size: 4rem;
                    // }

                    // @media screen and (min-width: 992px) and (max-width: 1023px) {
                    //     font-size: 3.8rem;
                    // }

                    @media (min-width: 320px) and (max-width: 991px) {
                        font-weight: 400;
                    }
                }

                &:last-child {
                    // font-size: 3rem;
                    line-height: 1;

                    // @media (min-width: 991px) {
                    //     font-size: 4rem;
                    //     line-height: 4rem;
                    // }
                }

                @media (max-width: 600px) {
                    font-size: 1.6rem;
                }
            }

            @media (min-width: 320px) and (max-width: 767px) {
                flex-direction: row;
                justify-content: center;
                align-items: center;

                p {
                    &:first-child {
                        padding-bottom: 0;
                    }

                    &:last-child {
                        // padding-left: 1rem;
                        padding-bottom: 0;
                    }
                }
            }

            @media (min-width: 1100px) {
                width: calc(30% - 70px);
                margin-right: 7rem;
                border-right: 1px solid #ffffff;
                padding: 0;
            }
        }

        .list-quarterly-results {
            width: calc(100%);
            display: flex;
            flex-wrap: wrap;

            .icon-item {
                &:first-child,
                &:nth-child(3),
                &:last-child {
                    .play-audio {
                        display: none;
                    }

                    .link {
                        position: absolute;
                        z-index: 9;

                        // img {
                        //     transform: translate(-50%, 50%);
                        //     @media (min-width: 300px) and (max-width: 480px) {
                        //         //transform: translate(-51%, 42%);
                        //         transform: translate(-49%, 45%);
                        //         width: 60px;
                        //         height: 60px;
                        //     }
                        // }
                    }
                }

                // &:nth-child(2) {
                //     .play-audio {
                //         z-index: 5;
                //         position: absolute;
                //         top: 0;
                //         left: 0;
                //         width: 100%;
                //         font-size: 0;
                //         cursor: pointer;
                //     }

                //     .link {
                //         //display: none;
                //         position: unset;
                //         top: unset;
                //         //z-index: 9;
                //     }
                // }
            }

            @media (min-width: 1100px) {
                width: calc(70%);
            }

            .icon-item {
                width: calc(100% / 2);
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-bottom: 5rem;

                @media (min-width: 768px) and (max-width: 1023px) {
                    width: calc(100% / 4);
                }

                &:last-child {
                    margin-bottom: 0;
                }

                @media (min-width: 991px) {
                    width: calc(100% / 4);
                    margin-bottom: 0;
                }

                .box-icon {
                    border: 4px solid #ffffff;
                    width: 12rem;
                    height: 12rem;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 50%;
                    margin-bottom: 2rem;
                    transition: all 0.3s ease-in-out;
                    position: relative;
                    z-index: 1;

                    &:hover {
                        z-index: 1;

                        &:after {
                            content: "";
                            background: #ffffff;
                            position: absolute;
                            width: 12rem;
                            height: 12rem;
                            border-radius: 50%;
                            opacity: 0.4;
                            z-index: 0;
                        }
                    }

                    .link-to {
                        z-index: 1;
                    }

                    img {
                        object-fit: cover;
                    }
                }

                .text-title {
                    color: #ffffff;
                    // font-size: 2.4rem;
                    line-height: 1;
                    font-weight: 400;
                    text-align: center;

                    @media (min-width: 481px) and (max-width: 991px) {
                        padding-left: 0.5rem;
                    }

                    @media (min-width: 320px) and (max-width: 991px) {
                        font-size: 1.8rem !important;
                    }
                }
            }

            @media (max-width: 600px) {
                margin-top: 2.5rem;
            }
        }
    }
}

// Icons
.circle-wrap {
    position: relative;
    width: 16rem;
    height: 16rem;
    border-radius: 50%;
    margin: auto auto 2rem auto;

    @media (min-width: 481px) and (max-width: 767px) {
        width: 8rem;
        height: 8rem;
    }

    @media (min-width: 300px) and (max-width: 480px) {
        width: 6rem;
        height: 6rem;
    }

    @media (min-width: 768px) and (max-width: 1023px) {
        width: 14rem;
        height: 14rem;
    }
}

.circle-wrap .circle .mask,
.circle-wrap .circle .fill {
    width: 16rem;
    height: 16rem;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 50%;

    @media (min-width: 481px) and (max-width: 767px) {
        width: 8rem;
        height: 8rem;
    }

    @media (min-width: 300px) and (max-width: 480px) {
        width: 6rem;
        height: 6rem;
    }

    @media (min-width: 768px) and (max-width: 1023px) {
        width: 13rem;
        height: 13rem;
    }
}

.circle-wrap .circle .mask {
    clip: rect(0rem, 18rem, 18rem, 8rem);
    @media (min-width: 300px) and (max-width: 480px) {
        clip: rect(0rem, 18rem, 18rem, 8rem);
    }
}

.circle-wrap .inside-circle {
    width: 15.2rem;
    height: 15.2rem;
    border-radius: 100%;
    background-color: transparent;
    line-height: 15.2rem;
    text-align: center;
    margin: 0.2rem;
    color: #ffffff;
    position: absolute;
    top: 0.2rem;
    left: 0.2rem;
    font-weight: 700;

    @media (min-width: 481px) and (max-width: 767px) {
        width: 8rem;
        height: 8rem;
    }

    @media (min-width: 320px) and (max-width: 480px) {
        width: 6rem;
        height: 6rem;
    }

    @media (min-width: 768px) and (max-width: 1023px) {
        width: 12.2rem;
        height: 12.2rem;
    }

    &:hover {
        &:after {
            background: rgba(255, 255, 255, 0.4);
            transition: all 0.3s ease-in-out;
        }
    }

    &:after {
        display: block;
        position: absolute;
        content: "";
        width: calc(100% + 0.8rem);
        height: calc(100% + 0.8rem);
        top: -0.4rem;
        left: -0.4rem;
        border-radius: 100%;
        border: 0.4rem solid rgba(255, 255, 255, 0.4);

        @media (min-width: 320px) and (max-width: 480px) {
            width: 100%;
            height: 100%;
        }
    }

    img {
        width: 7.5rem;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        @media (min-width: 481px) and (max-width: 767px) {
            width: 5rem;
        }

        @media (min-width: 768px) and (max-width: 1023px) {
            width: 5.5rem;
        }
    }
}

@media (min-width: 300px) and (max-width: 480px) {
    .circle-wrap .inside-circle {
        img {
            width: 3rem;
            height: 3rem;
            top: 45%;
            left: 45%;
        }
    }
}

// .mask .fill {
//     clip: rect(0rem, 8rem, 18rem, 0rem);
//     border: 0.4rem solid #ffffff;
// }

@keyframes fill {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(180deg);
    }
}

.icon-item {
    &:hover {
        .mask.full,
        .circle .fill {
            animation: fill ease-in-out 2s;
            transform: rotate(180deg);
        }
    }
}

.hp__quicklinks {
    z-index: 1;

    .container {
        background: #fff;
    }

    .ocean {
        height: 5%;
        width: 100%;
        position: relative;
        bottom: 0;
        left: 0;
        background: #fff;
        height: 28.4rem;
        z-index: 2;
    }

    .wave {
        background-image: url("/media/1zubfi12/wave.png");
        background-repeat: repeat-x;
        position: absolute;
        bottom: 5.4rem;
        width: 764.8rem;
        height: 28.4rem;
        animation: wave 7s cubic-bezier(0.36, 0.45, 0.63, 0.53) infinite;
        transform: translate3d(0, 0, 0);
        z-index: -1;
    }

    .discover-more-link {
        padding-top: 5rem;

        p {
            font-size: 1.4rem;
            @media (min-width: 990px) {
                font-size: 1.8rem;
            }

            // @media (min-width: 991px) and (max-width: 1279px) {
            //     font-size: 1.8rem;
            // }
        }

        a {
            margin-top: 2rem;
            font-style: normal;
            font-weight: 400;
            font-size: 1.6rem;
            line-height: 1;
            color: $colorBlack;
            transition: all 0.3s linear;

            &:hover {
                color: $colorBlue;
            }
        }
    }

    .quicklinks__list {
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 2rem;
        @media (min-width: 600px) {
            padding-top: 4rem;
        }

        .ql__item {
            .ql__link {
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;

                .ql__icon {
                    width: 4rem;
                    height: 4rem;
                    @media (min-width: 600px) {
                        width: 8rem;
                        height: 8rem;
                    }

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .ql__title {
                    margin-top: 2rem;
                    font-style: normal;
                    font-weight: 400;
                    font-size: 1.2rem;
                    line-height: 1;
                    text-align: center;
                    color: $colorBlack;
                    transition: all 0.3s linear;
                    @media (min-width: 600px) {
                        font-size: 1.6rem;
                    }
                }

                &:hover {
                    .ql__title {
                        color: $colorBlue;
                    }
                }
            }
        }
    }

    // @media screen and (max-width: 991px) {
    //     .ocean {
    //         height: 45rem;
    //     }

    //     .wave {
    //         height: 47rem;
    //     }

    //     .quicklinks__list {
    //         flex-wrap: wrap;

    //         .ql__item {
    //             width: calc(100% / 4);

    //             &:nth-last-child(1),
    //             &:nth-last-child(2),
    //             &:nth-last-child(3) {
    //                 width: calc(100% / 3);
    //                 margin-top: 2rem;
    //                 @media (min-width: 600px) {
    //                     margin-top: 4rem;
    //                 }
    //             }

    //         }
    //     }

    // }

    @media screen and (max-width: 991px) {
        .ocean {
            height: 45rem;
        }

        .wave {
            height: 47rem;
        }
    }

    @media (min-width: 300px) and (max-width: 599px) {
        .ocean {
            height: 36rem;
        }

        .wave {
            height: 36rem;
        }
    }

    @media screen and (max-width: 991px) {
        .container {
            padding-bottom: 4rem;
        }

        .quicklinks__list {
            flex-wrap: wrap;
            justify-content: center;

            .ql__item {
                width: calc(100% / 3);
                margin-top: 2rem;
                @media (min-width: 600px) {
                    margin-top: 4rem;
                }

                &:nth-last-child(1),
                &:nth-last-child(2),
                &:nth-last-child(3) {
                    width: calc(100% / 3);
                    margin-top: 5rem;
                }
            }
        }
    }
}

@keyframes wave {
    0% {
        margin-left: 0;
    }

    100% {
        margin-left: -1912px;
    }
}
