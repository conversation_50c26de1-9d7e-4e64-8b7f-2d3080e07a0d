﻿.esg-reports-container {
    padding-top:10rem;
    .parent-back {
        img {
            width: 1.3rem;
            margin-right: 1rem;
        }

        a {
            font-weight: 400;
            font-size: 1.4rem;
            line-height: 150%;
            color: #0CB6F2;
        }
    }

    .title-report {
        font-size: 24px;
        background-color: #0cb6f2;
        color: #fff;
        text-align: center;
        padding: 15px 0;
        margin: 15px 0;
    }

    .list-report {
        margin-top: 4rem;
        display: flex;
        align-items: start;
        gap: 3.9rem;
        flex-wrap: wrap;
        &:nth-child(3) {
            margin-bottom: 5rem;
        }

        @media screen and (max-width: 768px) {
            gap:4rem;
        }

        .report {
            width: calc((100% / 3) - 2.66rem);

            @media screen and (max-width: 1200px) {
                width: calc(50% - 1.95rem);
            }

            @media screen and (max-width: 768px) {
                width: 100%;
            }

            .photo {
                margin-bottom: 2rem;
                text-align:center;
                img {
                    width: auto;
                    max-width:100%;
                    //height: 23.6rem;
                    object-fit: cover;
                }
            }

            .esg-report-title {
                text-align: center;

                a {
                    width: 100%;
                    font-weight: 400;
                    font-size: 1.8rem;
                    line-height: 150%;
                    color: #000000;
                    @media (min-width: 767px) {
                        font-size: 2.4rem;
                    }
                }
            }
        }
    }
}
