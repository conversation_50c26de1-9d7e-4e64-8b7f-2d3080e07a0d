.site-header {
  
  .container {
    height: 100%;
  }
}

:lang(cn),
:lang(hk) {
  body {
    .header {
      .row-top {
        .logo-pc {
          max-width: 196px;
          max-height: 36px;
          @media (max-width: 1024px) {
            max-width: 16rem !important;
          }
        }
      }
    }
  }
}

body .header {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 5;

  .row-top {
    position: relative;
    display: flex;
    position: relative;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    margin: 0;

    
    .brand-logo {
      @media (min-width: 200px) and (max-width: 767px) {
        margin-top: -1rem;
      }

      @media (min-width: 1200px) and (max-width: 1388px) {
        margin-top: -1.3rem;
      }
      
      @media (min-width: 1400px) and (max-width: 2500px) {
        margin-top: -1.9rem;
      }
    }

    .logo {
      width: 100%;
    }

    .row {
      justify-content: space-between;
      align-items: center;

      @media (min-width: 1025px) {
        justify-content: center;
      }
    }

    .off-canvas {
      display: none;

      @media (max-width: 1200px) {
        display: block;
      }
    }

    .wrap-right {
      display: flex;
      align-items: center;
      height: 70%;

      .wrap-lang {
        display: flex;
        align-items: center;
        height: 100%;

        a {
          margin-left: 1rem;
        }

        img {
          margin-right: 0.5rem;

          &.global-black {
            display: none;
          }
        }
      }

      @media (max-width: 600px) {
        transform: translateX(15px);
      }
    }

    @media (max-width: 1380px) {

      .logo-pc {
        max-width: 18rem;
        min-width: 18rem;
      }

      .logo-mobile {
        max-width: 12rem;
      }
    }

    @media (min-width: 1381px) and (max-width: 2900px) {
      .logo-pc { 
        min-width: 23rem;
      }
      
    }

    @media (min-width: 1025px) {

      .wrap-tools {
        display: none;
      }
    }

    @media (max-width: 1200px) {
      .menu-header {
        display: none;
      }
    }

    @media (max-width: 1024px) {
      padding-top: 1rem;
      padding-bottom: 1rem;

      .logo-pc {
        max-width: 16rem;
        min-width: 16rem;
      }
    }
  }

  .brand-logo .logo-sticky {
    display: none !important;

    @media (max-width: 1200px) {
      display: block !important;
    }
  }
  .brand-logo .logo-on-top {
    display: block !important;

    @media (max-width: 1200px) {
      display: none !important;
    }
  }

  .wrap-tools {
    display: flex;
    align-items: center;

    &.wrap-tools-left {
      .language {
        margin-left: 0;
        margin-right: 2rem;
      }
    }

    &.wrap-tools-right {
      .btn-menu-offcanvas {
        justify-content: end;
      }
    }
  }

  .language {
    position: relative;
    align-items: center;

    a {
      font-size: 1.6rem;
      color: #fff;

      @media (max-width: 1200px) {
        color: $colorBlack;
      }
    }
  }

  &.header-mobile-center {

    @media (max-width: 575px) {
      .brand-logo {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }

  &.base-sticky {
    &.headroom--unpinned,
    &.headroom--pinned.headroom--not-top {
      position: fixed;
      top: 0;
      width: 100%;
      animation: headerFix .3s ease 1;
      background-color: #fff;
      border-bottom: 1px solid rgba(13, 28, 33, 0.26);

      .brand-logo .logo-sticky {
        display: block !important;
      }
      .brand-logo .logo-on-top {
        display: none !important;
      }

      .wrap-lang img {
        &.global-white {
          display: none;
        }
        &.global-black {
          display: block;
        }
      }

      .language a {
        color: $colorBlack;
      }
    }

    @media (min-width: 1200px) {
      &.headroom--top {
        position: absolute;
        width: 100%;
      }
    }
  }

  &.dynamic-sticky {
    &.headroom--pinned {
      position: fixed;
      top: 0;
      width: 100%;
      transform: translateY(0);
      transition: all .75s ease-in-out;

      &.headroom--top {
        position: static;
      }
    }
    &.headroom--unpinned {
      position: fixed;
      top: 0;
      width: 100%;
      transform: translateY(-100%);
      transition: all .75s ease-in-out;
    }
  }

  @media (max-width: 1200px) {
    height: 7rem;
    background-color: #fff;
  }
}

.menuicon-label {
  align-items: center;
  justify-content: end;
  position: relative;
  width: 48px;
  height: auto;
  cursor: pointer;
  transform-origin: top;
  transition: transform 0.4s cubic-bezier(0.4, 0.01, 0.165, 0.99) 0s;

  .line-top {
    width: 17px;
    height: 1px;
    margin-left: 15px;
    background: #333;
    display: block;
    margin-bottom: 5px;
    transition: transform 0.4s cubic-bezier(0.4, 0.01, 0.165, 0.99) 0.2s;

    .active-menu & {
      transform: rotate(-45deg) translateY(2px) translateX(2px);
      transition: transform 0.25s cubic-bezier(0.4, 0.01, 0.165, 0.99) 0.2s;
    }
  }

  .line-middle {
    width: 17px;
    height: 1px;
    margin-left: 15px;
    background: #333;
    display: block;
    margin-bottom: 5px;
    margin-top: 5px;
    transition: transform 0.4s cubic-bezier(0.4, 0.01, 0.165, 0.99) 0.2s;

    .active-menu & {
      display: none;
    }
  }

  .line-bottom {
    width: 17px;
    height: 1px;
    margin-left: 15px;
    background: #333;
    display: block;
    transition: transform 0.4s cubic-bezier(0.4, 0.01, 0.165, 0.99) 0.2s;

    .active-menu & {
      transform: rotate(45deg) translateY(-6px) translateX(-2px);
      transition: transform 0.25s cubic-bezier(0.4, 0.01, 0.165, 0.99) 0.2s;
    }
  }

  .active-menu & {
    transform: rotate(90deg);
  }
}

.background-opacity {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(10, 10, 10, 0.45);
  opacity: 0;
  visibility: hidden;
  z-index: 3;

  body.open-search & {
    opacity: 1;
    visibility: visible;
  }
}