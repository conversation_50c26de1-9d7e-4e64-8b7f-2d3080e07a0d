var hpNewsAPI = hPNewsUrl;
$.getJSON(hpNewsAPI, function (response) {
  // posts = response;
  const  posts = response;
  posts.sort((a, b) => new Date(b.date) - new Date(a.date));
  var htmls = posts.map(function (post, index) {
    var day = post.date.slice(8, 10);
    var month = post.date.slice(5, 7);
    var year = post.date.slice(0, 4);
    var link = post.link.replaceAll("%26", "&");

    if (index < 4) {
      // return (
      //   '<div class="news-item"> <div class="title"> <a onclick="window.open(\'' + link + '\'', '_blank', 'location=yes,height=570,width=520,scrollbars=yes,status=yes');">' + post.title + '</a> </div> <div class="published-date">' + day + '/' + month + '/' + year + '</div> </div>'
      // );

      return `
        <div class="news-item">
            <div class="title">
                <a class="link text-font-size-24" onclick="window.open('${link}', '_blank', 'location=yes,height=600,width=600,scrollbars=yes,status=yes');">
                    ${post.title}
                </a>
            </div>
            <div class="published-date text-font-size-14">
                ${year}/${month}/${day}
            </div>
        </div>
      `;
    }
  });
  var html = htmls.join("");
  $("#hp-recent-news").html(html);
});
