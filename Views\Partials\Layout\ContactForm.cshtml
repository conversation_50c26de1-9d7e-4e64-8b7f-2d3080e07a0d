@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@using System.Net;
@using System.Net.Mail;
@using Newtonsoft.Json;
@using System.Net.Http;
@using System.Net.Http.Headers;
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor;
@using Microsoft.Extensions.Options;
@using Umbraco.Cms.Core.Configuration.Models;
@inject IOptions<GlobalSettings> globalSettings;

@{
    var iconReset = "/media/lwmdkuge/frame111.png";
    string submitMessage = "";
    string requiredText = Umbraco.GetDictionaryValue("Please enter this field");
    string firstName = Umbraco.GetDictionaryValue("First Name");
    string lastName = Umbraco.GetDictionaryValue("Last Name");
    string email = Umbraco.GetDictionaryValue("Email");
    string businessTitle = Umbraco.GetDictionaryValue("Business Title");
    string company = Umbraco.GetDictionaryValue("Company");
    string country = Umbraco.GetDictionaryValue("Country");
    string workPhone = Umbraco.GetDictionaryValue("Work Phone");
    string fax = Umbraco.GetDictionaryValue("fax");
    string investorType = Umbraco.GetDictionaryValue("Investor Type");
    string enquiryContent = Umbraco.GetDictionaryValue("Enquiry Content");
}

<form id="contact-page-form" method="post">
    <div class="first-name">
        <label for="firstName">@firstName</label>
        <input type="text" class="field-reset" name="firstName" id="first-name-id" value="" required
            oninvalid="this.setCustomValidity('@requiredText')" oninput="this.setCustomValidity('')" />
    </div>

    <div class="last-name">
        <label for="lastName">@lastName</label>
        <input type="text" class="field-reset" name="lastName" id="last-name-id" value="" required
            oninvalid="this.setCustomValidity('@requiredText')" oninput="this.setCustomValidity('')" />
    </div>

    <div class="email">
        <label for="email">@email</label>
        <input type="text" class="field-reset" name="email" id="email-id" value="" required
            oninvalid="this.setCustomValidity('@requiredText')" oninput="this.setCustomValidity('')" />
    </div>

    <div class="business-title">
        <label for="businessTitle">@businessTitle</label>
        <input type="text" class="field-reset" name="businessTitle" id="business-title-id" value="" 
            oninvalid="this.setCustomValidity('@requiredText')" oninput="this.setCustomValidity('')" />
    </div>

    <div class="company">
        <label for="company">@company</label>
        <input type="text" class="field-reset" name="company" id="company-id" value="" 
            oninvalid="this.setCustomValidity('@requiredText')" oninput="this.setCustomValidity('')" />
    </div>

    <div class="country">
        <label for="country">@country</label>
        <input type="text" class="field-reset" name="country" id="country-id" value="" 
            oninvalid="this.setCustomValidity('@requiredText')" oninput="this.setCustomValidity('')" />
    </div>

    <div class="work-phone">
        <label for="workPhone">@workPhone</label>
        <input type="text" class="field-reset" name="workPhone" id="work-phone-id" value=""
            oninvalid="this.setCustomValidity('@requiredText')" oninput="this.setCustomValidity('')" />
    </div>

    <div class="fax">
        <label for="fax">@fax</label>
        <input type="text" class="field-reset" name="fax" id="fax-id" value=""
            oninvalid="this.setCustomValidity('@requiredText')" oninput="this.setCustomValidity('')" />
    </div>

    <div class="investor-type">
        <label for="investorType">@investorType</label>
        <input type="text" class="field-reset" name="investorType" id="investor-type-id" value="" 
            oninvalid="this.setCustomValidity('@requiredText')" oninput="this.setCustomValidity('')" />
    </div>

    <div class="enquiry-content">
        <label for="enquiryContent">@enquiryContent</label>
        <textarea id="message" class="field-reset" type="text" value="" name="message" required
            oninvalid="this.setCustomValidity('@requiredText')" oninput="this.setCustomValidity('')"></textarea>
    </div>
    <div class="wrap-buttons">
        <button id="submit" name="submit" class="submit button" data-callback="onSubmit">
            @Umbraco.GetDictionaryValue("Submit")
        </button>
        <a href="#" class="reset">
            <img src="@iconReset" alt="iconReset">
            @Umbraco.GetDictionaryValue("Reset")
        </a>
    </div>
    <div class="message">
        <span class="cresponse" id="cresponse">
            
        </span>
    </div>
</form>


<script>
    function onSubmit(token) {
        $("#contact-page-form").submit();
    }

    function validate(event) {
        event.preventDefault();
        if (!document.getElementById('email').value) {
            alert("You must add text to the required field");
        } else {
            grecaptcha.execute();
        }
    }

    function onload() {
        var element = document.getElementById('submit');
        element.onclick = validate;
    }
</script>