@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;

@{
  var home = Model.Root();
  var shareholderMeetingPage = home.DescendantsOfType("shareholderMeetingPage")?.FirstOrDefault() ?? null;
  var shareholderContentGroup = shareholderMeetingPage.Value<IEnumerable<IPublishedElement>>("shareholderContentGroup");

  var dateText = Umbraco.GetDictionaryValue("Date", "Date");
  var titleText = Umbraco.GetDictionaryValue("Title and Summary", "Title and Summary");

  var pdfIcon = "/media/0xzbdlyq/pdf-icon.png";
}

<div class="shareholder-section">
  <div class="shareholder-content">
    @foreach (var content in shareholderContentGroup)
    {
      var shareholderContent = content.Value<IEnumerable<IPublishedElement>>("shareholderContent");

      var title = content.Value<String>("title");

      var publishedDate = content.Value<DateTime>("publishedDate") != null ?
      content.Value<DateTime>("publishedDate") : DateTime.Now;
      var date = publishedDate.ToString(@Umbraco.GetDictionaryValue("DateFormat V3"));

      <div class="shareholder-item">
        <div class="title-wrapper">
          <div class="title">
            <p class="text-font-size-24">@title</p>
          </div>

          <div class="date">
            <p>@date</p>
          </div>
        </div>

        <div class="shareholder">
          @foreach (var item in shareholderContent)
          {
            var shareTitle = item.Value<String>("title");

            var file = item.HasProperty("file") && item.HasValue("file") ?
            item.Value<IPublishedContent>("file")?.Url() ?? "" : "";

            @if (file != null && file.Any())
            {
              <div class="shareholder-info">
                <a class="shareholder-link" href="@file" target="_blank">
                  <img src="@pdfIcon" alt="@shareTitle">
                  <p>@shareTitle</p>
                </a>
              </div>
            }
          }
        </div>
      </div>
    }
  </div>
</div>
