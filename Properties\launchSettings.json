{"$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:54020", "sslPort": 44361}}, "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Umbraco.Web.UI": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "https://localhost:44361;http://localhost:54020", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}