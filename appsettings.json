{"$schema": "./appsettings-schema.json", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}}, "Umbraco": {"CMS": {"Global": {"Id": "f0b88530-6415-44c3-9f62-ac6db4fc8217", "SanitizeTinyMce": true, "MainDomLock": "FileSystemMainDomLock", "Smtp": {"From": "<EMAIL>", "Host": "mailer.euroland.com", "Port": 25, "Username": "smtpclient", "Password": "xUWV3UuWDf"}}, "Hosting": {"LocalTempStorageLocation": "EnvironmentTemp"}, "Examine": {"LuceneDirectoryFactory": "TempFileSystemDirectoryFactory"}, "Content": {"Error404Collection": [{"Culture": "default", "ContentKey": "bf0e0418-a9d5-4674-943e-924ffbf6b785"}], "ContentVersionCleanupPolicy": {"EnableCleanup": true}}}, "Storage": {"AzureBlob": {"Media": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=eaumbmedia;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ContainerName": "umb-bilibili"}}}}, "ConnectionStrings": {"umbracoDbDSN": "Server=**********;Database=umb-bilibili;User Id=test;Password=******", "umbracoDbDSN_ProviderName": "Microsoft.Data.SqlClient"}}