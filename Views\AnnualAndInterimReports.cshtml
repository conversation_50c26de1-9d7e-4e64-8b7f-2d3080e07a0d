@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
    var annualAndInterimReportsPage = Model.DescendantsOfType("annualAndInterimReportsPage")?.FirstOrDefault() ?? null;
    var annualAndInterimReportsPageContent = annualAndInterimReportsPage != null ?
    annualAndInterimReportsPage.Children().OrderBy(x => x.SortOrder).ToList() : null;

    var query = Context.Request.Query["tab"].ToString().ToLower();

    var parentTabContext = "";
    var fragment = Context.Request.Headers["Referer"].ToString();
    if (!string.IsNullOrEmpty(fragment) && fragment.Contains("#"))
    {
        parentTabContext = fragment.Substring(fragment.IndexOf("#"));
    }

    var parentTab = Context.Request.Query["parent"].ToString();
    if (!string.IsNullOrEmpty(parentTab))
    {
        parentTabContext = parentTab.StartsWith("#") ? parentTab : "#" + parentTab;
    }

    if (string.IsNullOrEmpty(parentTabContext))
    {
        parentTabContext = "#annual-interim-reports";
    }
}
<div class="annual-interim-reports-container" data-parent-tab="@parentTabContext">
    <div class="tab-head child-head">
        <ul class="nav">
            @if (annualAndInterimReportsPageContent != null && annualAndInterimReportsPageContent.Any())
            {
                foreach (var item in annualAndInterimReportsPageContent)
                {
                    var tabName = item.Name.ToLower().Replace(" ", "-").Replace("&", "and").Replace("'", "-");
                    var stockExchangeName = item.Value("stockExchangeName")?.ToString() ?? "";
                    var isActive = query == tabName || (string.IsNullOrEmpty(query) && annualAndInterimReportsPageContent.First() == item);

                    <li class="nav-item">
                        <a href="javascript:void(0)"
                        class="tab-link nav-link @(isActive ? "active" : "")"
                        data-tab="<EMAIL>"
                        data-slug="@tabName">
                            @stockExchangeName
                        </a>
                    </li>
                }
            }
        </ul>
    </div>

    <div class="tab-content">
        @if (annualAndInterimReportsPageContent != null && annualAndInterimReportsPageContent.Any())
        {
            foreach (var item in annualAndInterimReportsPageContent)
            {
                var notice = item.Value<string>("notice");
                var tabName = item.Name.ToLower().Replace(" ", "-").Replace("&", "and").Replace("'", "-");
                var isActive = query == tabName || (string.IsNullOrEmpty(query) && annualAndInterimReportsPageContent.First() == item);
                var typeDisplay = "list file";
                if(item.Name.Equals("HKEX")==true){
                    typeDisplay = "list photo";
                }

                <div class="content tab-contents @(isActive ? "active" : "")" id="<EMAIL>">
                    @{
                    generateReports(item, typeDisplay);
                    }
                    <div class="notice">
                    @Html.Raw(notice)
                    </div>
                </div>
            }
        }
    </div>

</div>
@functions {
    private void generateReports(IPublishedContent stockExchange, string typeDisplay)
    {
        var stockExchangeContent = stockExchange != null ?
                                        stockExchange.Children()
                                        .Where(x => x.IsVisible())
                                        .OrderByDescending(x => x.Value<int>("year"))
                                        .ThenByDescending(x => x.Value<DateTime>("publishedDate"))
                                        .ThenByDescending(x => x.SortOrder).ToList() : null;
        var pdfIcon = "/media/0xzbdlyq/pdf-icon.png";

        if (typeDisplay.Equals("list file")==true)
        {
            <div class="@stockExchange.Name.ToLower()">
                @foreach (var item in stockExchangeContent)
                {
                    var title = item.Value("title").ToString();
                    var fileUrl = "";
                    if (item.HasValue("file")&&item.Value<IPublishedContent>("file")!=null)
                    {
                        fileUrl = item.Value<IPublishedContent>("file").Url();
                    }
                    var publishedDate = item.Value<DateTime>("publishedDate").ToString(Umbraco.GetDictionaryValue("Date Format - MM/dd/yyyy"));
                    <a href="@fileUrl" target="_blank" title="@title">
                        <div class="item">
                            <div class="icon"><img src="@pdfIcon" alt="PDF Icon" /></div>
                            <div class="title">
                            @Html.Raw(title)
                            <p class="date text-font-size-14">@publishedDate</p>
                            </div>
                        </div>
                    </a>
                    
                }
            </div>

        }else if (typeDisplay.Equals("list photo")==true)
        {
            List<int> listYear = new List<int>();
            foreach (var item in stockExchangeContent)
            {
                var year = 0;
                if (item.Value<int>("year") > 0)
                {
                    year = item.Value<int>("year");
                    listYear.Add(year);
                }
            };
            var groupYear = listYear.Distinct();
            
            <div class="@stockExchange.Name.ToLower()">
                @foreach (var year in groupYear)
                {
                    var yearContent = stockExchange.Children()
                                                    .Where(x => x.IsVisible())
                                                    .Where(x => x.Value<int>("year") == year)
                                                    .OrderByDescending(x => x.Value<DateTime>("publishedDate"))
                                                    .ThenByDescending(x => x.SortOrder).ToList();
                    <div class="hkex-year-content">
                        <div class="year"><span class="text-font-size-40">@year</span></div>
                        <div class="content">
                            @foreach (var item in yearContent)
                            {
                                var title = item.Value("title").ToString();
                                var fileUrl = "";
                                if (item.HasValue("file")&&item.Value<IPublishedContent>("file")!=null)
                                {
                                    fileUrl = item.Value<IPublishedContent>("file").Url();
                                }
                                var photoUrl = "";
                                if (item.HasValue("file")&&item.Value<IPublishedContent>("photo")!=null)
                                {
                                    photoUrl = item.Value<IPublishedContent>("photo").Url();
                                }
                                <div class="report">
                                    <div class="photo">
                                      <a href="@fileUrl" title="@title">
                                        <img src="@photoUrl"/>
                                      </a>
                                    </div>
                                    <div class="title ">
                                      <a class="text-font-size-24" href="@fileUrl" title="@Html.Raw(title)">@Html.Raw(title)</a>
                                    </div>
                                </div>
                            }
                        </div>                        
                    </div>
                }                
            </div>            
        }
    }
}