@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
	  var annualAndInterimReportsPage = Model.DescendantsOfType("annualAndInterimReportsPage")?.FirstOrDefault() ?? null;
    var annualAndInterimReportsPageContent = annualAndInterimReportsPage != null ?
    annualAndInterimReportsPage.Children().OrderBy(x => x.SortOrder).ToList() : null;
    
}
<div class="annual-interim-reports-container">
    <div class="tab-head child-head">
    
        <ul class="nav" role="tablist">
            @if (annualAndInterimReportsPageContent != null && annualAndInterimReportsPageContent.Any())
            {
                int iIndex = 0;
                foreach (var item in annualAndInterimReportsPageContent)
                {
                    var tabName = item.Name.ToLower().Replace(" ", "-").Replace("&", "and").Replace("'", "-");
                    var pageUrl = "#";
                    var href = "#" + tabName;
                    var isTab = true;
                    <li class="nav-item device_buttons" role="presentation" data-button-count="@iIndex">
                        <a class="nav-link @(annualAndInterimReportsPageContent.First() == item ?"active":"")" data-toggle="tab" href="@href"
                            aria-expanded="true">@item.Value("stockExchangeName")
                        </a>
                    </li>
                    
                    iIndex++;
                }
            }
        </ul>
        
    </div>
    <div class="tab-content">
        <div class="container">
        @if (annualAndInterimReportsPageContent != null && annualAndInterimReportsPageContent.Any())
        {
            int iIndex = 0;
            foreach (var item in annualAndInterimReportsPageContent)
            {
            var notice = item.Value<string>("notice");
            var tabName = item.Name.ToLower().Replace(" ", "-").Replace("&", "and").Replace("'", "-");
            var strClass = "tab-pane fade";
            if (annualAndInterimReportsPageContent.First() == item)
            {
                strClass = strClass + " active in show";
            }
            var typeDisplay = "list file";
            if(item.Name.Equals("HKEX")==true){
                typeDisplay = "list photo";
            }
            <div id="@tabName" class="@strClass content-tab" role="tabpanel" data-content-count="@iIndex">
                @{
                generateReports(item, typeDisplay);
                }
                <div class="notice">
                @Html.Raw(notice)
                </div>
            </div>
            iIndex++;
            }
        }
        </div>
    </div>
</div>
@functions {
    private void generateReports(IPublishedContent stockExchange, string typeDisplay)
    {
        var stockExchangeContent = stockExchange != null ?
                                        stockExchange.Children()
                                        .Where(x => x.IsVisible())
                                        .OrderByDescending(x => x.Value<int>("year"))
                                        .ThenByDescending(x => x.Value<DateTime>("publishedDate"))
                                        .ThenByDescending(x => x.SortOrder).ToList() : null;
        var pdfIcon = "/media/0xzbdlyq/pdf-icon.png";

        if (typeDisplay.Equals("list file")==true)
        {
            <div class="@stockExchange.Name.ToLower()">
                @foreach (var item in stockExchangeContent)
                {
                    var title = item.Value("title").ToString();
                    var fileUrl = "";
                    if (item.HasValue("file")&&item.Value<IPublishedContent>("file")!=null)
                    {
                        fileUrl = item.Value<IPublishedContent>("file").Url();
                    }
                    var publishedDate = item.Value<DateTime>("publishedDate").ToString(Umbraco.GetDictionaryValue("Date Format - MM/dd/yyyy"));
                    <a href="@fileUrl" target="_blank" title="@title">
                        <div class="item">
                            <div class="icon"><img src="@pdfIcon" alt="PDF Icon" /></div>
                            <div class="title">
                            @Html.Raw(title)
                            <p class="date text-font-size-14">@publishedDate</p>
                            </div>
                        </div>
                    </a>
                }
            </div>

        }else if (typeDisplay.Equals("list photo")==true)
        {
            List<int> listYear = new List<int>();
            foreach (var item in stockExchangeContent)
            {
                var year = 0;
                if (item.Value<int>("year") > 0)
                {
                    year = item.Value<int>("year");
                    listYear.Add(year);
                }
            };
            var groupYear = listYear.Distinct();
            
            <div class="@stockExchange.Name.ToLower()">
                @foreach (var year in groupYear)
                {
                    var yearContent = stockExchange.Children()
                                                    .Where(x => x.IsVisible())
                                                    .Where(x => x.Value<int>("year") == year)
                                                    .OrderByDescending(x => x.Value<DateTime>("publishedDate"))
                                                    .ThenByDescending(x => x.SortOrder).ToList();
                    <div class="hkex-year-content">
                        <div class="year"><span class="text-font-size-40">@year</span></div>
                        <div class="content">
                            @foreach (var item in yearContent)
                            {
                                var title = item.Value("title").ToString();
                                var fileUrl = "";
                                if (item.HasValue("file")&&item.Value<IPublishedContent>("file")!=null)
                                {
                                    fileUrl = item.Value<IPublishedContent>("file").Url();
                                }
                                var photoUrl = "";
                                if (item.HasValue("file")&&item.Value<IPublishedContent>("photo")!=null)
                                {
                                    photoUrl = item.Value<IPublishedContent>("photo").Url();
                                }
                                <div class="report">
                                    <div class="photo">
                                      <a href="@fileUrl" title="@title">
                                        <img src="@photoUrl"/>
                                      </a>
                                    </div>
                                    <div class="title ">
                                      <a class="text-font-size-24" href="@fileUrl" title="@Html.Raw(title)">@Html.Raw(title)</a>
                                    </div>
                                </div>
                            }
                        </div>                        
                    </div>
                }                
            </div>            
        }
    }

}