$(document).ready(function () {
    $(".faqs-container .head-info").click(function (e) {

        if ($(this).next(".desc").is(":visible")) {
            $(".faqs-container .item").removeClass("active")
            $(this).parent().removeClass("active")
            $(".desc").slideUp()
            e.stopImmediatePropagation();
        }
        else {
            
            $(".faqs-container .item").removeClass("active")
            $(this).parent(".item").addClass("active")
            $(".desc").slideUp()
            $(this).next(".desc").slideDown()
            e.stopImmediatePropagation();
        }
    });


    $('.box:first-child').addClass('d-block');
    $('.tab-header:first-child').addClass('active');

    $(".tab-header").each(function () {
        $(this).on("click", function () {
            var listItems = $(".tab-header");

            for (let i = 0; i < listItems.length; i++) {
                if (this != listItems[i]) {
                    listItems[i].classList.remove("active");
                }
            }

            if (this.classList.contains("active")) {
                this.classList.remove("active");
            } else if (!this.classList.contains("active")) {
                this.classList.add("active");
            }

            console.log($(this).data())
            var count = $(this).data('buttonCount')
            console.log('count ' + count)


            // ES5
            $('.box').each(function(index, item)  {
                if (item.getAttribute('data-content-count') != count) {
                    item.classList.remove('d-block')
                }

                // console.log('content count ' + item.getAttribute('data-content-count'))
                if (item.getAttribute('data-content-count') == count) {
                    if (!item.classList.contains('d-block')) {
                        console.log("add?")
                        item.classList.add('d-block')
                    } else if (item.classList.contains('d-block')) {
                        console.log("remove?")
                        item.classList.remove('d-block')
                    }
                }
            })
        });
    });

    
});