.news-item-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 10rem;

  @media (max-width: 1200px) {
    flex-direction: column-reverse;
    margin-top: 5rem;
  }

  .news-release-content {
    max-width: 104rem;
    width: 100%;
    padding-right: 2rem;
  }

  .parent-link {
    a {
      font-style: normal;
      font-weight: 400;
      font-size: 1.4rem;
      line-height: 1.4;
      color: #0cb6f2;
    }
  }

  .title {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin-top: 5rem;
    padding-bottom: 2rem;
    border-bottom: 0.1rem solid rgba(13, 28, 33, 0.12);

    h3 {
      font-style: normal;
      font-weight: 400;
      font-size: 2.4rem;
      line-height: 3.2rem;
      color: #0d1c21;
      width: calc(100% - 10rem);
    }

    p {
      font-style: normal;
      font-weight: 400;
      font-size: 1.4rem;
      line-height: 3.2rem;
      text-align: right;
      color: #0cb6f2;
      width: 10rem;
    }
  }

  .content {
    padding-top: 4rem;

    p {
      font-style: normal;
      font-weight: 400;
      font-size: 1.6rem;
      line-height: 1.4;
      color: rgba(13, 28, 33, 0.54);
    }

    a {
      color: rgba(13, 28, 33, 0.54);
      text-decoration: none;
    }
  }

  .navigator {
    margin-top: 5rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .nav {
      a {
        font-style: normal;
        font-weight: 400;
        font-size: 1.4rem;
        line-height: 1.4;
        color: #0cb6f2;
      }
    }
  }

  .share-section {
    margin-top: 10rem;

    @media (max-width: 1200px) {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      margin-top: 0;
      margin-bottom: 3rem;
    }

    .pdf {
      @media (max-width: 1200px) {
        width: 10rem;
      }

      a {
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
        align-items: center;
        font-style: normal;
        font-weight: 400;
        font-size: 1.4rem;
        line-height: 1.4;
        color: #0d1c21;

        @media (max-width: 1200px) {
          text-align: center;
        }

        img {
          width: 4rem;
          height: 4rem;
          object-fit: cover;
          margin-bottom: 1rem;
        }
      }
    }

    .share {
      margin-top: 10rem;
      display: flex;
      flex-wrap: wrap;
      flex-direction: column;
      align-items: center;
      margin-bottom: 2rem;

      @media (max-width: 1200px) {
        flex-direction: row;
        width: calc(100% - 10rem);
        justify-content: flex-end;
        margin-top: 0;
      }

      p {
        font-style: normal;
        font-weight: 400;
        font-size: 1.4rem;
        line-height: 150%;
        text-align: center;
        color: #0d1c21;

        @media (min-width: 1201px) {
          margin-bottom: 2rem;
        }
      }

      a {
        color: #0d1c21;
        text-decoration: none;
      }

      .share-icon {
        &:last-child {
          margin-bottom: 0;
        }

        a::before {
          font-size: 3.2rem;
        }

        @media (min-width: 1201px) {
          margin-bottom: 2rem;
        }

        @media (max-width: 1200px) {
          margin-left: 2rem;
        }
      }

      .twitter {
        .btn i {
          background-image: gray;
        }
      }
    }
  }
}
