﻿<div ng-controller="Our.Umbraco.TheDashboard.Controller as vm" class="theDashboard">
    
    <div>

        <umb-box>
            <umb-box-header title-key="theDashboard_recentActivities" description-key="theDashboard_recentActivitiesDescription" ></umb-box-header>
            <umb-box-content>

                <div ng-repeat="item in vm.recentActivities.allItems" class="recentActivity">

                    <umb-avatar name="{{item.user.name}}"
                                size="xs"
                                color="secondary"
                                img-src="{{item.user.avatar[0]}}"
                                img-srcset="{{item.user.avatar[1]}} 2x, {{item.user.avatar[2]}} 3x">
                    </umb-avatar>

                    <div>
                        <span class="theDashboard-datetime">{{item.datestampFormatted}}</span>
                        <p ng-if="item.activityType =='Save'">
                            {{item.user.name}} <localize key="theDashboard_saved">saved</localize> <a href="/umbraco/#/content/content/edit/{{item.nodeId}}">{{item.nodeName}}</a> <localize key="theDashboard_butDidNotPublish">but did not publish</localize>.
                        </p>
                        <p ng-if="item.activityType =='SaveAndScheduled'">
                            {{item.user.name}} <localize key="theDashboard_savedAndScheduled">saved and scheduled</localize> <a href="/umbraco/#/content/content/edit/{{item.nodeId}}">{{item.nodeName}}</a> <localize key="theDashboard_forPublishingAt">for publishing at</localize> {{item.scheduledPublishDateFormatted}}.
                        </p>
                        <p ng-if="item.activityType =='Publish'">
                            {{item.user.name}} <localize key="theDashboard_savedAndPublished">saved and published</localize> <a href="/umbraco/#/content/content/edit/{{item.nodeId}}">{{item.nodeName}}</a>.
                        </p>
                        <p ng-if="item.activityType =='Unpublish'">
                            {{item.user.name}} <localize key="theDashboard_unpublished">unpublished</localize> <a href="/umbraco/#/content/content/edit/{{item.nodeId}}">{{item.nodeName}}</a>.
                        </p>
                        <p ng-if="item.activityType =='RecycleBin'">
                            {{item.user.name}} <localize key="theDashboard_moved">moved</localize> <a href="/umbraco/#/content/content/edit/{{item.nodeId}}">{{item.nodeName}}</a> <localize key="theDashboard_to">to</localize> <a href="/umbraco/#/content/content/recyclebin"><localize key="theDashboard_recycleBin">recycle bin</localize></a>.
                        </p>
                        <p ng-if="item.activityType =='RollBack'">
                            {{item.user.name}} <localize key="theDashboard_rolledBack">rolled back</localize> <a href="/umbraco/#/content/content/edit/{{item.nodeId}}">{{item.nodeName}}</a>
                        </p>
                    </div>
                </div>

            </umb-box-content>
        </umb-box>

    </div>
    <div>
        
        <umb-box class="theDashboard-pending-wrapper">
            <umb-box-header
                title-key="theDashboard_pendingContent"
                description-key="theDashboard_pendingContentDescription"
                ng-click="showPending = !showPending">
                <span>{{vm.pending.count}}</span>
                <div>
                    <svg ng-if="!showUnpublished" width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.5 12L0.00480941 0.75L12.9952 0.750001L6.5 12Z" fill="#C4C4C4" />
                    </svg>
                    <svg ng-if="showUnpublished" width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.5 0L12.9952 11.25L0.00480938 11.25L6.5 0Z" fill="#C4C4C4" />
                    </svg>
                </div>
            </umb-box-header>
            <umb-box-content ng-if="showPending">
                <div ng-repeat="item in vm.pending.items" class="recentActivity">
                    
                    <umb-avatar name="{{item.user.name}}"
                                size="xs"
                                color="secondary"
                                img-src="{{item.user.avatar[0]}}"
                                img-srcset="{{item.user.avatar[1]}} 2x, {{item.user.avatar[2]}} 3x">
                    </umb-avatar>

                    <div>
                        <span class="theDashboard-datetime">{{item.datestampFormatted}}</span>
                        <p ng-if="item.activityType =='Save'">
                            <localize key="theDashboard_Saved">saved</localize> <a href="/umbraco/#/content/content/edit/{{item.nodeId}}">{{item.nodeName}}</a> <localize key="theDashboard_butDidNotPublish">but did not publish</localize>.
                        </p>
                    </div>

                </div>
            </umb-box-content>
        </umb-box>

        <umb-box>
            <umb-box-header title-key="theDashboard_yourRecentActivity" description-key="theDashboard_yourRecentActivitiesDescription"></umb-box-header>
            <umb-box-content>

                <div ng-repeat="item in vm.recentActivities.yourItems" class="recentActivity">
                    <div>
                        <span class="theDashboard-datetime">{{item.datestampFormatted}}</span>
                        <p ng-if="item.activityType =='Save'">
                            <localize key="theDashboard_Saved">Saved</localize> <a href="/umbraco/#/content/content/edit/{{item.nodeId}}">{{item.nodeName}}</a> <localize key="theDashboard_butDidNotPublish">but did not publish</localize>.
                        </p>
                        <p ng-if="item.activityType =='SaveAndScheduled'">
                            <localize key="theDashboard_SavedAndScheduled">Saved and scheduled</localize> <a href="/umbraco/#/content/content/edit/{{item.nodeId}}">{{item.nodeName}}</a> <localize key="theDashboard_forPublishingAt">for publishing at</localize> {{item.scheduledPublishDateFormatted}}.
                        </p>
                        <p ng-if="item.activityType =='Publish'">
                            <localize key="theDashboard_SavedAndPublished">Saved and published</localize> <a href="/umbraco/#/content/content/edit/{{item.nodeId}}">{{item.nodeName}}</a>.
                        </p>
                        <p ng-if="item.activityType =='Unpublish'">
                            <localize key="theDashboard_Unpublished">Unpublished</localize> <a href="/umbraco/#/content/content/edit/{{item.nodeId}}">{{item.nodeName}}</a>.
                        </p>
                        <p ng-if="item.activityType =='RecycleBin'">
                            <localize key="theDashboard_Moved">Moved</localize> <a href="/umbraco/#/content/content/edit/{{item.nodeId}}">{{item.nodeName}}</a> <localize key="theDashboard_to">to</localize> <a href="/umbraco/#/content/content/recyclebin"><localize key="theDashboard_recycleBin">recycle bin</localize></a>.
                        </p>
                        <p ng-if="item.activityType =='RollBack'">
                            <localize key="theDashboard_RolledBack">Rolled back</localize> <a href="/umbraco/#/content/content/edit/{{item.nodeId}}">{{item.nodeName}}</a>
                        </p>
                    </div>
                </div>

            </umb-box-content>
        </umb-box>
    </div>
    <div>
        
        <div class="number" ng-repeat="counter in vm.counters.counters">
            <div class="dot dot-style-{{counter.style}}">{{counter.count}}</div>

            <p ng-if="counter.clickUrl && counter.clickUrl !== ''">
                <a href="{{counter.clickUrl}}">
                    {{counter.text}}
                </a>
            </p>
            <p ng-if="counter.clickElement && counter.clickElement !== ''">
                <a href="" ng-click="vm.clickElement(counter.clickElement)">
                    {{counter.text}}
                </a>
            </p>
            <p ng-if="(counter.clickUrl == undefined || counter.clickUrl === '') && (counter.clickElement == undefined || counter.clickElement === '')">{{counter.text}}</p>
        </div>
        
    </div>
</div>
