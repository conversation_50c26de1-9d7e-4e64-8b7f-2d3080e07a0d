﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
    Layout = "MasterLayout.cshtml";
    var home = Model.Root();
    var title = "";
    if (Model.HasValue("pageTitle") && Model.Value("pageTitle") != null)
    {
        title = Model.Value("pageTitle").ToString();
    }
}

<div class="childpage-content  @Model.Name.ToLower().Replace(" ", "-").Replace("'", "-")">
    <div class="fluid-container">
        <section class="childpage-banner banner">
            @{
                Html.RenderPartial("Layout/Banner");
            }
        </section>        
        <div class="title-box">
        </div>
        <div class="inner-content">
            <div class="container-layout">
                <div class="right-content">
                    <div class="childpage-ir-layout">
                        @RenderBody()
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>