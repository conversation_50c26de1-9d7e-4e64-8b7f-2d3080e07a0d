﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
	var invesrtorRelationContactPage = Model.DescendantsOfType("invesrtorRelationContactPage")?.FirstOrDefault() ?? null;
	var listLocation = invesrtorRelationContactPage != null ?
	invesrtorRelationContactPage.Children().OrderBy(x => x.SortOrder).ToList() : null;
	var emailIcon = "/media/amhbgsbb/email-icon.png";
	var phoneIcon = "/media/ercnhao2/phone-icon.png";
}
<div class="ir-contact-container">
	@if (listLocation != null && listLocation.Any())
	{
		foreach (var item in listLocation)
		{
			var location = item.Value("location").ToString();
			var iRContactOffices = item.Value<IEnumerable<IPublishedElement>>("iRContactOffice").ToList();
			<div class="location-wrap">
				<div class="location">@location</div>
				<div class="offices">
					@foreach (var office in iRContactOffices)
					{
						var locationName = office.Value("locationName").ToString();
						var personName = office.Value("personName").ToString();
						var phoneNumber = office.Value("phoneNumber").ToString();
						var email = office.Value("email").ToString();
						<div class="item">
							<div class="location-name">@locationName</div>
							<div class="person-name text-font-size-24">@personName</div>
							<div class="phone-number ">
								<img src="@phoneIcon" alt="phone icon" />
								<a>@phoneNumber</a>
							</div>
							<div class="email">
								<img src="@emailIcon" alt="email icon" />
								<a href="mailto: @email">@email</a>
							</div>
						</div>
					}
				</div>
			</div>
		}
	}
</div>