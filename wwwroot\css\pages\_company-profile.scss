﻿.company-profile-container {
    .content-block {
        margin-bottom: 8rem;
        display: flex;
        gap: 4rem;

        &:first-child {
            .photo {
                order: 1;
            }
        }

        &:last-child {
            .photo {
                order: 1;
            }
        }

        @media (min-width: 768px) and (max-width: 1200px) {
            display: block;
            gap: unset;
            .photo {
                overflow: hidden;
                float: right;
                margin-left: 40px;
                margin-bottom: 20px;
                max-width: 50%;
            }
            .content {
                margin-bottom: 7rem;
                max-width: unset !important;
            }

            &:last-child {
                .photo {
                    float: left;
                    margin-left: 0;
                    margin-right: 40px;
                }
            }
        }

        @media screen and (max-width: 991px) {
            flex-direction: column-reverse;
            margin-bottom: 6rem;
            flex-wrap: wrap;
        }

        &:nth-child(2n) {
            flex-direction: row-reverse;

            @media screen and (max-width: 991px) {
                flex-direction: column-reverse;
            }
        }

        &:last-child {
            @media screen and (max-width: 991px) {
                margin-bottom: 0;
            }
        }


        .content {
            max-width: 80rem;

            @media screen and (max-width: 991px) {
            }

            p {
                font-weight: 400;
                //font-size: 1.6rem;
                line-height: 150%;
                color: $colorGrey;
            }

            .block-title {
                font-weight: 400;
                //font-size: 2.4rem;
                line-height: 150%;
                color: $colorBlack;
            }
        }

        .photo {

            img {
                width: 56rem;

                @media screen and (max-width: 1200px) {
                    width: 42rem;
                }
            }

            @media screen and (max-width: 991px) {
                width: 100%;
                text-align: center;

                img {
                    width: auto;
                    max-width: 100%;
                }
            }
        }
    }
}
