@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;
@{
    var home = Model.Root();
    var newsReleasePage = home.DescendantsOfType("newsReleasePage")?.FirstOrDefault() ?? null;
    @* IEnumerable<IPublishedContent> listNews = newsReleasePage.Children()
        .Where(x => x.IsVisible())
        .OrderByDescending(x => x.Value<DateTime>("publishedDate"))
        .ThenByDescending(x => x.SortOrder);

        if (listNews.Count() > 4)
        {
        listNews = listNews.Take(4);
        } *@

    var recentNewsTitle = home.Value("recentNewsTitle").ToString();

    var recentNewsPhotoUrl = "";
    if (home.HasValue("recentNewsPhoto") && home.Value<IPublishedContent>("recentNewsPhoto") != null)
    {
        recentNewsPhotoUrl = home.Value<IPublishedContent>("recentNewsPhoto").Url();
    }
}
<div class="container">
    <h3 class="title recent-news-title">@recentNewsTitle</h3>
    <div class="news-box-content">
        <div class="photo"><img src="@recentNewsPhotoUrl" alt="@recentNewsTitle" /></div>
        <div id="hp-recent-news" class="list-news">
            @* @foreach (var item in listNews)
                {
                var newsTitle = item.Value("pageTitle").ToString();
                var publishedDate = item.Value<DateTime>("publishedDate").ToString(Umbraco.GetDictionaryValue("Date Format -
                dd/MM/yyyy"));
                <div class="news-item">
                <div class="title"><a href="@item.Url()" target="_self" title="@newsTitle">@newsTitle</a></div>
                <div class="published-date">@publishedDate</div>
                </div>
                } *@
        </div>
    </div>
    <div class="btn-view-more">
        <a class="button" href="@newsReleasePage.Parent.Url()" target="_self" title="View More">
            @Umbraco.GetDictionaryValue("View More")
        </a>
    </div>
</div>
