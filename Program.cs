using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Umbraco.Cms.Core.Composing;
using Umbraco.Cms.Core.WebAssets;

namespace umb_bilibili
{
  public class Program
  {
    public static void Main(string[] args)
        => CreateHostBuilder(args)
            .Build()
            .Run();

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureUmbracoDefaults()
            .ConfigureWebHostDefaults(webBuilder =>
            {
              webBuilder.UseStaticWebAssets();
              webBuilder.UseStartup<Startup>();
            });
  }
  public class MyComponent : IComponent
  {
    private readonly IRuntimeMinifier _runtimeMinifier;

    public MyComponent(IRuntimeMinifier runtimeMinifier) => _runtimeMinifier = runtimeMinifier;

    public void Initialize()
    {
      _runtimeMinifier.CreateJsBundle("inline-js-bundle3",
          BundlingOptions.NotOptimizedAndComposite,
          new[] {
                    "~/scripts/lib/jquery.min.js",
                    "~/scripts/lib/bootstrap.min.js",
                    // "~/scripts/lib/bootstrap-icon.min.js",
                    // "~/scripts/lib/animate.min.js",
                    "~/scripts/lib/fullpage.min.js",
                    "~/scripts/lib/jquery.magnific-popup.min.js",
                    "~/scripts/lib/aos.js",
                    "~/scripts/lib/headroom.js",
                    "~/scripts/lib/jquery.simplePagination.min.js",
                    "~/scripts/lib/swiper-bundle.js",
                    "~/scripts/lib/jquery.nice-select.min.js",
                    "~/scripts/common/*.js",
                    "~/scripts/pages/*.js",
          });

      _runtimeMinifier.CreateCssBundle("inline-css-bundle",
          BundlingOptions.NotOptimizedAndComposite,
          new[] {
                    "~/css/lib/",
                    "~/css/common/main.css",
          });
    }

    public void Terminate() { }
  }

  public class MyComposer : ComponentComposer<MyComponent>
  { }
}
