﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
    var investorFAQsPage = Model.DescendantsOfType("investorFAQsPage")?.FirstOrDefault() ?? null;
    var listQuestions = investorFAQsPage.Children.Where(x => x.IsVisible()).OrderBy(x => x.SortOrder).ToList();
    var arrowIcon = "/media/qsveavwl/microsoftteams-image-41.png";
    var arrowIconActive = "/media/u5ypktsm/up-arrow.png";
}
<div class="faqs-container">
    @foreach (var item in listQuestions)
    {
        var question = item.Value("question").ToString();
        var answer = item.Value("answer").ToString();
        <div class="item">
            <div class="head-info">
                <div class="question">
                    @question
                </div>
                <div class="arrow">
                    <img class="img-icon" src="@arrowIcon" alt="Arrow icon" />
                    <img class="img-active" src="@arrowIconActive" alt="Arrow icon" />
                </div>
            </div>
            <div class="desc">@Html.Raw(answer)</div>
        </div>
    }
</div>