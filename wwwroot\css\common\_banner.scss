

.banner {
  position: relative;
  overflow: hidden;

  // @media (min-width: 1025px) and (max-width: 1260px) {
  //   margin-bottom: -8rem;
  // }

  // @media (min-width: 768px) and (max-width: 1024px) {
  //   margin-bottom: -8rem;
  // }
  
  @media (min-width: 1025px) {
    .childpage-content.home & {
      position: fixed !important;
      width: 100%;
      top: 0;
      left: 0;
    }
  }

  .first-line {

    body .childpage-content:not(.home) & {
      text-align: left;
      font-size: 6.4rem;
      line-height: 1;
      transform: translateY(3rem);

      @media (max-width: 1500px) {
        font-size: 4.5rem;
      }
      @media (max-width: 1200px) {
        font-size: 3.5rem;
      }

      @media (max-width: 600px) {
        font-size: 2.8rem;
      }
    }

    .home & {
      margin-bottom: 4rem;

      @media (max-width: 800px) {
        margin-bottom: 2rem;
      }
    }
  }

  .swiper-slide {
      position: relative;
      height: 300px;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      @media (min-width: 200px) and (max-width: 991px) {
        background-position: 82%;
        position: relative;
        &:after {
          content: "";
          background-color: #333;
          position: absolute;
          width: 100%;
          height: 100%;
          opacity: 0.2;
        }
      }
  }

  .container-fluid {
      height: 100%;
  }

  .wrap-content {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 1.5rem;
      text-align: center;

      .content {
          width: 100%;
          transform: translateY(-5rem);

          h2 {
            font-size: 9.6rem;
            line-height: 1;
            color: #fff;
            font-weight: 700;

            @media (max-width: 1500px) {
                font-size: 4.5rem;
            }
            @media (max-width: 1200px) {
                font-size: 3.5rem;
            }

            @media (max-width: 600px) {
                font-size: 2.8rem;
            }
            p {
              font-size: 6.4rem;
              line-height: 1.4;
              color: #fff;
              font-weight: 700;

              @media (max-width: 1500px) {
                  font-size: 4.5rem;
              }
              @media (max-width: 1200px) {
                  font-size: 3.5rem;
              }

              @media (max-width: 600px) {
                  font-size: 2.8rem;
              }
            }
              
          }

          .content-description {
            width: 65%;
            margin: 0 auto 4rem auto;

            @media (max-width: 1300px) {
              width: 70%;
            }

            @media (max-width: 800px) {
              width: 100%;
              margin: 0 auto 2rem auto;
            }
          }

          @media (max-width: 1025px) {
            transform: none;
          }

          @media (max-width: 600px) {
            width: 100%;
          }
      }

      .ticker-tools {
        width: 40%;

        @media (max-width: 1300px) {
          width: 55%;
        }

        @media (max-width: 600px) {
          width: 100%;
        }
      }

      p {
          font-size: 2.4rem;
          line-height: 1.5;
          font-weight: 400;
          text-align: left;
          margin-top: 0.2rem;
          margin-bottom: 1.5rem;
          color: #fff;

          @media(max-width: 600px) {
            font-size: 1.6rem;
          }
      }

      .stock-quote {
        min-width: 50rem;
        text-align: center;
        background-color: #000;
        opacity: .8;
        padding: 3%;

        h3 {
          color: #fff;
          font-size: 2.5rem;
        }

        p {
          text-align: center;
          color: #fff;
          font-size: 1.2rem;
          margin: 1rem 0 0 0;
        }

        img {
          width: 100%;
        }

        @media(max-width: 1000px) {
          min-width: unset;
        }
      }

      .qr-btn-mobile {
        display: none;
      }

      @media (max-width: 600px) {
        body.home & {
          margin-left: -15px;
        }
        padding: 15px;
        margin: 30px 0;
        flex-direction: column;
        justify-content: center;
      }

      @media (max-width: 1025px) {
        .qr-btn-mobile {
          display: block;
          text-align: center;
          margin-top: 3rem;
        }
      }
  }

  .banner-wrap {
      position: relative;

      &.dot-false {
        .swiper-pagination {
          display: none;
        }
      }

      &.arrow-false {
        .swiper-button-next,
        .swiper-button-prev {
          display: none;
        }
      }

      &.banner-fullpage-true {
        .swiper-slide {
          height: 100vh !important;

          @media (max-width: 800px) {
            height: 540px !important;
          }
          @media (max-width: 500px) {
            height: 440px !important;
          }
        }   
      }
  }

  .swiper-button-next:after,
  .swiper-button-prev:after {
      font-weight: bold;
  }

  .swiper-pagination-bullet {
    width: 1.5rem;
    height: 1.5rem;
    background-color: rgba(0,0,0,.8);

    &.swiper-pagination-bullet-active {
      background-color: $color-primary;
      opacity: .8;
    }
  }

  .swiper-button-prev,
  .swiper-container-rtl .swiper-button-next,
  .swiper-button-next,
  .swiper-button-prev,
  .swiper-button-next.swiper-button-disabled,
  .swiper-button-prev.swiper-button-disabled {
      color: #fff;
      z-index: 1;
      opacity: .3;

      @media (max-width: 600px) {
          display: none;
      }

      &:hover {
        opacity: 1;
      }
  }

  .swiper-button-prev {
      left: 30px;
  }

  .swiper-button-next {
      right: 30px;
  }

  .container {
      position: absolute;
      top: 0;
      padding: 0;
      left: 50%;
      z-index: 1;
      display: flex;
      align-items: center;
      height: 100%;
      transform: translateX(-50%);

      @media (max-width: 1025px) {
          right: 0;
          padding: 0 15px;
      }

      @media (max-width: 600px) {
          padding: 0;
      }
  }

  .ticker-chart,
  .stock-quote {
      height: auto;
      min-height: 22.5rem;
      margin-left: auto;

      iframe {
        min-height: 22.5rem;
      }

      @media (max-width: 600px) {
          margin-right: auto;
          margin-left: 0;
          margin-top: 30px;
      }
  } 

  .ticker-chart {
    width: 100%;
  }

  .stock-quote {
    width: 25rem;

    @media (max-width: 1000px) {
      width: auto;
    }
  }

  &.childpage-banner .wrap-content {
    width: 100%;
  }

  .arrow-down {
    display: none;
    position: absolute;
    bottom: 4rem;
    left: 49%;
    transform: translateX(-50%);
    z-index: 1;
    animation-name: loop;
    animation-duration: 3s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;

    .home & {
      display: block;
    }



    @media (min-width: 600px) and (max-width: 800px) {
      left: 47.5%;
    }

    @media (min-width: 500px) and (max-width: 599px) {
      left: 47%;
    }

    @media (min-width: 421px) and (max-width: 499px) {
      left: 46%;
    }

    @media (min-width: 320px) and (max-width: 420px) {
      left: 45%;
    }
  }

  @media(max-width: 1200px) {
    margin-top: 7rem;
  }
}

/* Add some content at the bottom of the video/page */
.background-video {
  position: relative;

  /* Style the video: 100% width and height to cover the entire window */
  #myVideo {
    position: fixed;
    z-index: -1;
    &::-webkit-media-controls {
      display:none !important;
    }

    &::-webkit-media-controls-panel {
      display: none!important;
      -webkit-appearance: none;
    
    }
    
    &::--webkit-media-controls-play-button {
      display: none!important;
      -webkit-appearance: none;
    
    }
    
    &::-webkit-media-controls-start-playback-button {
      display: none!important;
      -webkit-appearance: none;
    }

    @media (min-aspect-ratio: 16/9) {
      width:100%;
      height: auto;
    }

    @media (max-aspect-ratio: 16/9) {
      width:auto;
      height: 100%;
    }

    @media (min-width: 900px) and (max-width: 1200px) {
      transform: translateX(-15%);
    }
    @media (max-width: 600px) {
      transform: translateX(-25%) !important;
    }
  }
}

@media (max-width: 1600px) {
  
  body .childpage-content:not(.home) .childpage-banner.banner .swiper-slide {
    min-height: unset;
    max-height: unset;
    max-height: 30rem;
  }

  body .section.homepage .swiper-slide {
    min-height: 80vh;
  }
}

// Cut to homepage later
.banner-ticket {
  position: absolute;
  bottom: -0vh;
  left: 0;
  width: 100%;
  height: 26vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: cover !important;
  z-index: 2;

  iframe {
    position: absolute;
    top: 6rem;
    left: 50%;
    transform: translateX(-50%);
    
    @media (max-width: 1025px) {
      min-width: 87% !important;
    }
    @media (max-width: 1000px) {
      min-width: 80% !important;
    }

    @media (min-width: 1200px) and (max-width: 1600px) {
      top: 5rem !important;
    }
  }

  @media (max-width: 1600px) {
    background-position: bottom !important;
    background-repeat: no-repeat !important;
    bottom: -20vh;
    border-radius: 20px 20px 0 0 !important;
  }

  @media (min-width: 1000px) and (max-width: 1599px) {
    background-position: top !important;
    bottom: 0;
    height: 35vh !important;
  }
}

.ticket-mobile {
  padding: 3rem 0;

  // #iframe-ticket-banner {
  //   height: 180px !important;

  //   @media (max-width: 600px) {
  //     height: 430px !important;
  //   }
  // }
  
}

.swiper-wrapper.slide-active-3:not(.slide-active-2) {
  .banner-ticket {
    // background: url('/media/2qqhxven/bg-white-big.png') !important;
    bottom: 0;
    // height: 65vh;
    background-size: cover !important;

    iframe {
      top: 25%;
    }
  }
}

.swiper-slide.background-video {
  position: relative;

  &:after {
    position: absolute;
    content: '';
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0,0,0,.2);
    z-index: 1;
  }

  .container {
    z-index: 2;
  }
}

.image-qr-mobile {
  display: none;
}

@media (max-width: 1025px) {
  .image-qr-mobile {
    display: block;
    position: fixed;
    top: 50%;
    left: 50%;
    background-color: rgba(10, 10, 10, 0.45);
    opacity: 0;
    visibility: hidden;
    transform: translate(-50%, -50%);
    z-index: 10;
  
    body.open-qr-mobile & {
      opacity: 1;
      visibility: visible;
    }
  }

  body {
    &.open-qr-mobile {
      .background-opacity {
        opacity: 1;
        visibility: visible;
      }
    }
  }
}