.esg-container {
    overflow-x: hidden;
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */

    &::-webkit-scrollbar {
        display: none;
    }
}

.esg {
    .inner-content {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        margin: 0;
    }
}

.esg-banner {
    height: 100vh;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: right;

    .esg__wrapper {
        padding-top: 28rem;

        .esg__title {
            color: #fff;
            margin-bottom: 6rem;
            max-width: 50%;

            p {
                // font-family: "Sofia Pro";
                font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
            }
        }

        .esg__content {
            max-width: 50%;

            p {
                font-style: normal;
                font-weight: 400;
                // font-size: 2.2rem;
                line-height: 1.5;
                color: #ffffff;

                @media (min-width: 768px) and (max-width: 1400px) {
                    font-size: 2rem;
                }

                // @media (min-width: 1400px) {
                //     // margin-top: 2rem;
                //     font-size: 2.4rem;
                // }
            }

            ul {
                list-style: disc;
                margin-top: 4rem;

                li {
                    font-style: normal;
                    font-weight: 400;
                    font-size: 2rem;
                    line-height: 1.5;
                    color: #ffffff;
                    margin-left: 2rem;
                }
            }
        }

        .esg__sign {
            margin-top: 6rem;
            max-width: 50%;

            @media (min-width: 1366px) and (max-width: 1400px) {
                margin-top: 2rem;
            }

            p {
                font-style: normal;
                font-weight: 400;
                // font-size: 2.2rem;
                text-align: right;
                color: #ffffff;

                @media (min-width: 768px) and (max-width: 1400px) {
                    font-size: 2rem;
                }

                // @media (min-width: 1400px) {
                //     // margin-top: 2rem;
                //     font-size: 2.4rem;
                // }

                &:last-child {
                    margin-top: 1rem;
                    font-style: normal;
                    font-weight: 400;
                    font-size: 1.6rem;
                    line-height: 1;
                    text-align: right;
                }
            }
        }
    }

    @media screen and (max-width: 1700px) {
        .esg__wrapper {
            padding-top: 15rem;
        }
    }

    @media screen and (max-width: 1023px) {
        background-position: 70% 100%;

        // height: auto;
        .esg__wrapper {
            .esg__title {
                max-width: 100%;
            }

            .esg__content {
                max-width: 100%;
            }

            .esg__sign {
                max-width: 100%;
            }
        }
    }
}

.esg-reports {
    padding: 0 1.5rem 8rem 1.5rem;

    @media (min-width: 767px) and (max-width: 1279px) {
        padding: 12rem 1.5rem 13.2rem;
    }

    @media (min-width: 1280px) {
        padding: 16rem 1.5rem;
    }

    .esg-reports__title {
        text-align: center;
        margin-bottom: 4rem;

        @media (min-width: 767px) {
            margin-bottom: 8rem;
        }
    }

    .esg-reports__wrapper {
        display: flex;
        align-items: stretch;
        justify-content: space-between;

        .esg-reports__photo {
            width: 50%;
            &:lang(en) {
                height: 100%;
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                &:lang(en) {
                    object-fit: contain;
                }
            }
        }

        .esg-reports__content {
            width: 50%;
            padding: 0 2rem 0 12rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            p {
                font-style: normal;
                font-weight: 400;
                // font-size: 2.4rem;
                line-height: 1.5;
                color: $colorGrey;
            }

            .esg-reports__view-more {
                margin: 0;

                &:hover {
                    color: #fff;
                    background: #ff558c;
                    opacity: 0.8;
                }
            }
        }
    }

    @media screen and (max-width: 1600px) {
        //padding: 10rem 1.5rem;

        .esg-reports__wrapper {
            align-items: flex-start;

            // .esg-reports__photo {}

            .esg-reports__content {
                padding: 0 2rem 0 5rem;

                .esg-reports__view-more {
                    margin-top: 2rem;
                }
            }
        }
    }

    @media (min-width: 768px) and (max-width: 1279px) {
        .esg-reports__wrapper {
            padding: 0 7.7rem;
            display: block;

            .esg-reports__photo {
                width: 100%;
                padding-right: 0;
            }

            .esg-reports__content {
                width: 100%;
                display: block;
                padding: 0;
                margin-top: 4rem;

                p {
                    font-size: 1.6rem;
                }

                .esg-reports__view-more {
                    margin: 2rem auto 0;
                    font-size: 1.6rem;
                }
            }
        }
    }

    @media screen and (max-width: 767px) {
        .esg-reports__wrapper {
            display: block;

            .esg-reports__photo {
                width: 50%;
                float: left;
                padding-right: 3rem;
            }

            .esg-reports__content {
                width: 100%;
                display: block;
                padding: 0;

                .esg-reports__view-more {
                    margin: 2rem auto 0;
                }
            }
        }
    }

    @media screen and (max-width: 767px) {
        .esg-reports__wrapper {
            display: flex;
            flex-direction: column;

            .esg-reports__photo {
                width: 100%;
                float: unset;
                padding-right: 0;
            }

            .esg-reports__content {
                width: 100%;
                display: block;
                padding: 2rem 0 0;

                .esg-reports__view-more {
                    margin: 2rem auto 0;
                }
            }
        }
    }
}

@supports (-webkit-overflow-scrolling: touch) {
    .esg__honors-and-awards {
        @media (min-width: 320px) and (max-width: 767px) {
            background-attachment: local;
            background-position: center;
        }
    }
}

.esg__honors-and-awards {
    background-size: cover;
    background-repeat: no-repeat;
    background-position: right;
    background-attachment: fixed;
    // height: 72.3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12rem 0;

    .esg-haa__title {
        color: #fff;
        text-align: center;
        margin-bottom: 6.6rem;

        @media (max-width: 600px) {
            margin-bottom: 4rem;
        }
    }

    .esg-haa__list {
        margin-left: -4rem;
        display: flex;
        align-items: stretch;
        justify-content: flex-start;
        flex-wrap: wrap;

        .esg-haa__item {
            width: calc((100% / 4) - 4rem);
            margin-left: 4rem;
            margin-bottom: 4rem;
            display: flex;
            flex-direction: column;
            align-items: center;

            // justify-content: space-between;
            .esg-haa__item-icon {
                margin-bottom: 1.6rem;

                @media (max-width: 600px) {
                    display: none;
                }
            }

            .esg-haa__item-title {
                font-style: normal;
                font-weight: 700;
                // font-size: 2rem;
                line-height: 1.5;
                text-align: center;
                color: #fff;
                margin-bottom: 1rem;
                // height: 60px;

                @media (min-width: 320px) and (max-width: 767px) {
                    height: auto;
                }

                // @media (min-width: 768px) and (max-width: 1024px) {
                //     font-size: 1.9rem;
                // }

                html[lang="cn"] &,
                html[lang="hk"] & {
                    // -webkit-font-family: "Microsoft YaHei Bold";
                    // -webkit-font-smoothing: antialiased;

                    font-size: 1.6rem;
                    // height: 4.5rem;

                    @media (min-width: 767px) {
                        font-size: 1.8rem;
                        // height: 2rem;
                    }

                    // @media (max-width: 480px) {
                    //     font-family: "Microsoft YaHei UI", "Microsoft YaHei-Regular", -apple-system;
                    //     // font-family: -apple-system;
                    // }
                }
            }

            .esg-haa__item-sign {
                //margin-top: auto;
                font-style: normal;
                font-weight: 400;
                // font-size: 1.4rem;
                line-height: 1.5;
                text-align: center;
                color: #fff;
            }
        }

        //Start: Duynt edit 25/04/2023
        // @media screen and (min-width: 1500px) {
        //     &:lang(en) {
        //         .esg-haa__item:nth-child(7) {
        //             .esg-haa__item-sign {
        //                 margin-top: 6rem;
        //             }
        //         }
        //     }

        //     &:lang(hk),
        //     &:lang(cn) {

        //         .esg-haa__item:nth-child(2),
        //         .esg-haa__item:nth-child(3),
        //         .esg-haa__item:nth-child(6),
        //         .esg-haa__item:nth-child(7) {
        //             .esg-haa__item-sign {
        //                 margin-top: 4rem;
        //             }
        //         }
        //     }
        // }

        //End: Duynt edit 25/04/2023
    }

    @media screen and (max-width: 1500px) {
        height: auto;
        padding: 10rem 1.5rem 10rem;

        .esg-haa__list {
            margin-left: -3rem;
            justify-content: flex-start;

            .esg-haa__item {
                width: calc((100% / 2) - 3rem);
                margin-left: 3rem;
                margin-bottom: 4rem;

                // .esg-haa__item-icon {}

                .esg-haa__item-title {
                    height: auto;
                    line-height: auto;
                }

                // .esg-haa__item-sign {}
            }
        }
    }

    @media screen and (max-width: 1023px) {
    }

    @media screen and (max-width: 991px) {
        .esg-haa__list {
            //margin-left: -3rem;
            //justify-content: flex-start;

            .esg-haa__item {
                width: calc((100% / 2));
                // margin-left: 3rem;
                // margin-bottom: 4rem;
            }
        }
    }

    @media screen and (max-width: 991px) {
        height: auto;
        padding: 7rem 0 5rem 0;

        .esg-haa__list {
            // margin-left: -3rem;
            // justify-content: flex-start;
            margin-left: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;

            .esg-haa__item {
                width: calc(50% - 2rem);
                margin-left: 0;
                margin-bottom: 2rem;
                // margin-left: 3rem;
            }
        }
    }

    @media (max-width: 600px) {
        padding: 8rem 0;
    }
}

.esg-ratings-container {
    padding: 16rem 1.5rem 15rem;

    @media (min-width: 200px) and (max-width: 767px) {
        padding: 8rem 1.5rem;
    }

    .esg-ratings__title {
        text-align: center;
        margin-bottom: 8rem;
        // font-family: "Sofia Pro";
        font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
    }

    .esg-ratings__wrapper {
        display: flex;
        align-items: stretch;
        justify-content: space-between;

        .esg-ratings__photo {
            width: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
                max-width: 65rem;
                width: 100%;
                height: auto;
                object-fit: cover;
            }
        }

        .esg-ratings__content {
            width: 50%;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: center;
            padding-left: 5rem;

            .esg-ratings__title-intro {
                font-style: normal;
                font-weight: 400;
                font-size: 2.4rem;
                line-height: 1.5;
                color: $colorBlack;
                margin-bottom: 4rem;

                @media screen and (max-width: 991px) {
                    font-size: 1.8rem;
                }

                // @media (max-width: 480px) {
                //     font-family: "Microsoft YaHei UI", "Microsoft YaHei-Regular", -apple-system;
                // }
                p {
                    // font-family: "Sofia Pro";
                    font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
                }

                span {
                    font-weight: 700;
                    font-size: 6.4rem;
                    line-height: 1;
                    color: $colorBlue;
                    padding-left: 4rem;

                    @media screen and (max-width: 991px) {
                        font-size: 2.8rem;
                    }
                }
            }

            .esg-ratings__intro {
                font-style: normal;
                font-weight: 400;
                // font-size: 1.6;
                line-height: 1.5;
                color: $colorGrey;

                p {
                    // font-family: "Sofia Pro";
                    font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
                    position: relative;
                    &.dot {
                        margin-left: 1.6rem;
                        &::before {
                            content: " ";
                            width: 0.6rem;
                            height: 0.6rem;
                            background-color: rgba(13, 28, 33, 0.54);
                            border-radius: 50%;
                            display: inline-block;
                            position: absolute;
                            left: -14px;
                            top: 10px;
                        }
                    }
                }
            }
        }
    }

    @media (min-width: 991px) and (max-width: 1280px) {
        padding: 8rem 7.7rem;
    }

    @media screen and (max-width: 992px) {
        padding: 8rem 1.5rem;
    }

    @media screen and (max-width: 1200px) {
        .esg-ratings__wrapper {
            .esg-ratings__content {
                padding-left: 5rem;
            }
        }
    }

    @media screen and (max-width: 767px) {
        flex-wrap: wrap;

        .esg-ratings__wrapper {
            flex-wrap: wrap;

            .esg-ratings__photo {
                width: 100%;
            }

            .esg-ratings__content {
                width: 100%;
                margin-top: 3rem;
                padding-left: 0;
            }
        }
    }
}

.bilibili-public-welfare-container {
    padding: 8rem 1.5rem 8rem 1.5rem;
    background-color: $colorBlue;

    @media (min-width: 990px) {
        padding: 12rem 1.5rem 13rem;
    }

    @media (min-width: 991px) and (max-width: 1279px) {
        padding: 12rem 7.7rem 13rem;
    }

    // .container {
    //     @media (min-width: 75rem) {
    //         //1200px
    //         max-width: 112rem;
    //         width: 100%;
    //         margin: 0 auto;
    //     }
    // }

    .bpw__title {
        text-align: center;
        color: #fff;
        margin-bottom: 6rem;
    }

    .bpw__content {
        font-style: normal;
        font-weight: 400;
        font-size: 1.6rem;
        line-height: 1.5;
        color: #fff;
        text-align: center;
        margin: 0 auto 4rem;

        @media (min-width: 320px) and (max-width: 480px) {
            font-size: 1.4rem;
        }
    }

    .bpw__list {
        display: flex;
        flex-wrap: wrap;
        // align-items: stretch;
        // justify-content: space-between;
        //margin-left: -7.3rem;

        .item {
            // width: calc((100% / 3) - 7.3rem);
            // margin-left: 7.3rem;
            // text-align: center;
            width: calc(100% / 3);
            display: flex;
            flex-direction: column;
            align-items: center;

            &:nth-child(2) {
                padding: 0;

                @media (min-width: 1025px) {
                    padding: 0 1.5rem;
                }
            }

            .box-count {
                margin-bottom: 1rem;

                .number-count {
                    font-style: normal;
                    font-weight: 700;
                    // font-size: 6.4rem;
                    line-height: 1;
                    color: #fff;
                    // font-family: "Sofia Pro";
                    font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
                    span {
                        font-size: 4rem;
                        @media screen and (min-width: 1024px) and (max-width: 1470px) {
                            font-size: 2.5rem;
                        }

                        @media screen and (min-width: 992px) and (max-width: 1023px) {
                            font-size: 2.4rem;
                        }

                        @media screen and (max-width: 991px) {
                            font-size: 2.2rem;
                        }
                    }
                }
            }

            .content-count {
                font-style: normal;
                font-weight: 400;
                // font-size: 2.4rem;
                line-height: 1.5;
                color: #ffffff;
                text-align: center;
            }
        }
    }

    .bpw__view-more {
        margin: 6rem auto 2rem;

        &:hover {
            color: #fff;
        }
    }

    .bpw__date {
        font-style: normal;
        font-weight: 400;
        // font-size: 1.4rem;
        line-height: 1.5;
        color: #ffffff;
        // font-family: "Sofia Pro";
        font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
    }

    @media screen and (max-width: 1470px) {
        .bpw__list {
            .item {
                .box-count {
                    .number-count {
                        font-size: 4rem;
                    }
                }

                // .content-count {}
            }
        }
    }

    @media screen and (max-width: 1025px) {
        .bpw__list {
            .item {
                .box-count {
                    .number-count {
                        font-size: 3.8rem;
                    }
                }
            }
        }
    }

    @media screen and (max-width: 991px) {
        .bpw__list {
            .item {
                .box-count {
                    .number-count {
                        font-size: 3.6rem;
                    }
                }

                // .content-count {}
            }
        }
    }

    @media screen and (max-width: 767px) {
        .bpw__list {
            flex-wrap: wrap;
            justify-content: center;
            //margin-left: -2rem;

            .item {
                width: calc(100%);
                //margin-left: 2rem;
                margin-bottom: 4rem;

                &:last-child {
                    margin-bottom: 0;
                }

                .box-count {
                    .number-count {
                        font-size: 3.6rem;
                    }
                }

                // .content-count {}
            }
        }
    }
}
