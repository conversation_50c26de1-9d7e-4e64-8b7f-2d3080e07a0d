$(document).ready(function () {
  $(".sub-menu a").click(function () {
    var href = $(this).attr("href").split("#");
    var hash = href[1];

    // Skip if this is a dynamic tab hash (handled by specific script)
    if (hash && hash.includes('annual-and-interim-reports') && hash.includes('?tab=')) {
      return;
    }

    //console.log(href[1])
    activaTab(hash);
  });

  // Handle hash changes globally
  $(window).on('hashchange', function() {
    var hash = location.hash.substr(1);

    // If switching to annual-and-interim-reports with tab parameter,
    // let the specific script handle it
    if (hash.includes('annual-and-interim-reports') && hash.includes('?tab=')) {
      // Re-initialize the specific component after a short delay
      setTimeout(function() {
        if (typeof window.reinitializeAnnualInterimReports === 'function') {
          window.reinitializeAnnualInterimReports();
        }
      }, 100);
      return;
    }

    // Otherwise handle normally
    naviTab();
  });
  //investor relation tab
  $(".tab-head .nav a").click(function () {
    var href = $(this).attr("href").split("#");
    //console.log(href[1])
    //activaTab(href[1]);
  });
  //active tab
  if ($(".tabcontent-container").length > 0) {
    naviTab();

    $(".nav li").each(function () {
      var link = $(this).children("a").first();
      var href = link.attr("href");

      link.click(function () {
        ChangeUrl(href.replace("#", ""), href);
      });
    });
  }
});
//local active
function activaTab(tab) {
  // Clean tab - remove query parameters for selector
  var cleanTab = tab;
  if (tab.includes('?')) {
    cleanTab = tab.substring(0, tab.indexOf('?'));
  }
  $('.tab-head .nav a[href="#' + cleanTab + '"]').tab("show");
}
function ChangeUrl(page, url) {
  if (typeof history.pushState != "undefined") {
    var obj = { Page: page, Url: url };
    history.pushState(obj, obj.Page, obj.Url);
  } else {
    alert("Browser does not support HTML5.");
  }
}
function naviTab() {
  var hash = location.hash.substr(1);
  if (hash.length > 0) {
    // Skip if this is a dynamic tab hash (handled by specific script)
    if (hash.includes('annual-and-interim-reports') && hash.includes('?tab=')) {
      return;
    }

    // Clean hash - remove query parameters for selector
    var cleanHash = hash;
    if (hash.includes('?')) {
      cleanHash = hash.substring(0, hash.indexOf('?'));
    }

    $(".tab-head .nav li").removeClass("active");
    $(".tab-head .nav li a").removeClass("active");
    $(".tab-head .nav li a[href='#" + cleanHash.toLowerCase() + "']")
      .parent()
      .addClass("active");
    $(".tab-head .nav li a[href='#" + cleanHash.toLowerCase() + "']").addClass(
      "active"
    );
    $(".tab-content .tab-pane").removeClass("active show");
    var targetActive = $(".tab-content #" + cleanHash.toLowerCase());
    targetActive.addClass("active show");

    targetActive = targetActive.find("iframe");
    if (
      targetActive.attr("data-src") !== undefined &&
      targetActive.attr("data-src").length > 0
    ) {
      targetActive.attr("src", targetActive.attr("data-src"));
    }
    if (
      targetActive.attr("ea-src") !== undefined &&
      targetActive.attr("ea-src").length > 0
    ) {
      targetActive.attr("src", targetActive.attr("ea-src"));
    }

    targetActive.on("load", function () {
      if ($(".loading-icon").length > 0) {
        $(".loading-icon").hide();
      }
    });
  }
}
