$(document).ready(function () {
  $(".sub-menu a").click(function () {
    var href = $(this).attr("href").split("#");
    //console.log(href[1])
    activaTab(href[1]);
  });
  //investor relation tab
  $(".tab-head .nav a").click(function () {
    var href = $(this).attr("href").split("#");
    //console.log(href[1])
    //activaTab(href[1]);
  });
  //active tab
  if ($(".tabcontent-container").length > 0) {
    naviTab();

    $(".nav li").each(function () {
      var link = $(this).children("a").first();
      var href = link.attr("href");

      link.click(function () {
        ChangeUrl(href.replace("#", ""), href);
      });
    });
  }
});
//local active
function activaTab(tab) {
  $('.tab-head .nav a[href="#' + tab + '"]').tab("show");
}
function ChangeUrl(page, url) {
  if (typeof history.pushState != "undefined") {
    var obj = { Page: page, Url: url };
    history.pushState(obj, obj.Page, obj.Url);
  } else {
    alert("Browser does not support HTML5.");
  }
}
function naviTab() {
  var hash = location.hash.substr(1);
  if (hash.length > 0) {
    $(".tab-head .nav li").removeClass("active");
    $(".tab-head .nav li a").removeClass("active");
    $(".tab-head .nav li a[href='#" + hash.toLowerCase() + "']")
      .parent()
      .addClass("active");
    $(".tab-head .nav li a[href='#" + hash.toLowerCase() + "']").addClass(
      "active"
    );
    $(".tab-content .tab-pane").removeClass("active show");
    var targetActive = $(".tab-content #" + hash.toLowerCase());
    targetActive.addClass("active show");

    targetActive = targetActive.find("iframe");
    if (
      targetActive.attr("data-src") !== undefined &&
      targetActive.attr("data-src").length > 0
    ) {
      targetActive.attr("src", targetActive.attr("data-src"));
    }
    if (
      targetActive.attr("ea-src") !== undefined &&
      targetActive.attr("ea-src").length > 0
    ) {
      targetActive.attr("src", targetActive.attr("ea-src"));
    }

    targetActive.on("load", function () {
      if ($(".loading-icon").length > 0) {
        $(".loading-icon").hide();
      }
    });
  }
}
