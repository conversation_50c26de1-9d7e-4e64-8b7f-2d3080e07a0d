﻿.corporate-governance-container {
    .page-desc {
        margin-bottom: 4rem;

        p {
            font-weight: 400;
            //font-size: 2.4rem;
            line-height: 150%;
            text-align: left;
            color: $colorBlack;
        }
    }

    .committee-table {
        margin-bottom: 7.5rem;
        @media (min-width: 200px) and (max-width: 767px) {
            .box-table {
                overflow-y: scroll;
            }
        }

        .table-title {
            font-weight: 400;
            font-size: 1.4rem;
            line-height: 150%;
            text-align: left;
            color: $colorGrey;
            margin-bottom: 2rem;
            @media (min-width: 768px) {
                font-size: 1.6rem;
            }
        }

        table {
            margin-bottom: 2rem;
            border-bottom: 1px solid rgba(13, 28, 33, .04);;
            thead {
                tr {
                    background-color: $colorBlue;

                    td {
                        width: 20%;
                        text-align: center;
                        vertical-align: middle;
                        height: 6rem;
                        color: #ffffff;

                        @media screen and (max-width: 767px) {
                            padding: 0.5rem;
                            font-size: 1.4rem;
                        }
                    }

                    td:first-child {
                        //width: 28%;
                        width: 20%;
                    }

                    td:last-child {
                        width: 25%;
                    }
                }
            }

            tbody {
                tr {
                    &:nth-child(2n + 1) {
                        background-color: rgba(13, 28, 33, 0.04);
                    }

                    td {
                        text-align: center;
                        vertical-align: middle;
                        height: 6.4rem;
                    }

                    td:first-child {
                        text-align: left;
                        font-weight: 400;
                        font-size: 1.6rem;
                        line-height: 150%;
                        color: #000000;
                        padding-left: 5rem;
                        cursor: pointer;

                        @media screen and (max-width: 1200px) {
                            padding-left: 4rem;
                        }

                        @media screen and (max-width: 767px) {
                            padding-left: 2rem;
                            font-size: 1.4rem;
                        }
                    }
                }
            }
        }

        .icon-note {
            display: flex;
            justify-content: left;

            .icon-item {
                display: flex;
                margin-right: 5rem;
                align-items: center;
                @media (min-width: 200px) and (max-width: 767px) {
                    .text {
                        font-size: 1.4rem;
                    }
                }

                .icon {
                    margin-right: 1.3rem;
                }
            }

            .icon-item:last-child {
                margin-right: 0;
            }
        }

        .corporate-governance-modal {
            .modal-dialog {
                max-width: 100%;
                max-height: unset;
                top: 50%;
                left: 0;
                transform: translate(0, -50%);

                @media screen and (min-width: 992px) {
                    max-width: 90rem;
                    max-height: 58.1rem;
                }

                @media screen and (min-width: 1200px) {
                    max-width: 104rem;
                    max-height: 58.1rem;
                }

                .modal-content {
                    .modal-header {
                        position: relative;
                        background-color: $colorBlue;
                        padding: 4rem 6rem 2rem 6rem;

                        .close {
                            position: absolute;
                            top: 3rem;
                            right: 3rem;
                            font-size: 2.4rem;
                            padding: 0;
                            margin: 0;
                        }

                        .head-info {
                            .name {
                                font-weight: 400;
                                //font-size: 2.4rem;
                                line-height: 150%;
                                color: #ffffff;
                                margin-bottom: 1rem;
                            }

                            .position {
                                font-weight: 400;
                                //font-size: 1.6rem;
                                line-height: 150%;
                                color: #ffffff;
                            }
                        }
                    }

                    .modal-body {
                        padding: 3rem 6rem;
                        max-height: 47rem;

                        @media (min-width: 200px) and (max-width: 767px) {
                            overflow: scroll;
                        }

                        .info {
                            .cg-desc {
                                font-weight: 400;
                                font-size: 1.6rem;
                                line-height: 150%;
                                color: $colorGrey;
                                margin-bottom: 2.5rem;
                            }

                            .person-documents {
                                .documents-item {
                                    display: flex;
                                    gap: 2.3rem;
                                    justify-content: left;
                                    align-items: center;
                                    margin-bottom: 2.1rem;

                                    .icon {
                                    }

                                    .title {
                                        a {
                                            font-weight: 400;
                                            font-size: 1.4rem;
                                            line-height: 150%;
                                            color: #000000;
                                            @media (min-width: 768px) {
                                                font-size: 1.6rem;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .documents-box {
        .box-title {
            font-weight: 400;
            font-size: 1.4rem;
            line-height: 150%;
            text-align: left;
            color: $colorGrey;
            margin-bottom: 2rem;
            @media (min-width: 768px) {
                font-size: 1.6rem;
            }
        }

        .box-content {
            display: grid;
            grid-gap: 4rem;
            gap: 4rem;
            grid-template-columns: repeat(2, 1fr);

            @media screen and (max-width: 767px) {
                display: block;
            }

            .documents-group {
                .group-title {
                    font-weight: 400;
                    //font-size: 2.4rem;
                    line-height: 150%;
                    color: $colorBlack;
                    margin-bottom: 2rem;

                    @media (max-width: 800px) {
                        &.text-font-size-20 {
                            margin-top: 3rem;
                        }
                    }
                }

                .document-list {
                    a {
                        margin-bottom: 2rem;
                        display: block;
                        width: 100%;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }

                    .item {
                        background-color: rgba(13, 28, 33, 0.04);
                        display: flex;
                        padding: 1.5rem 5.2rem 1.7rem 5.2rem;
                        align-items: center;
                        cursor: pointer;
                        -webkit-transform: perspective(1px) translateZ(0);
                        transform: perspective(1px) translateZ(0);
                        /* Underline From Center */
                        &::before {
                            content: "";
                            position: absolute;
                            z-index: -1;
                            left: 51%;
                            right: 51%;
                            bottom: 0;
                            background: $colorBlue;
                            height: 0.2rem;
                            -webkit-transition-property: left, right;
                            transition-property: left, right;
                            -webkit-transition-duration: 0.4s;
                            transition-duration: 0.4s;
                            -webkit-transition-timing-function: ease-out;
                            transition-timing-function: ease-out;
                        }

                        &:hover::before {
                            left: 0;
                            right: 0;
                        }

                        .icon {
                            img {
                                width: 2.8rem;
                                height: 2.8rem;
                            }
                        }

                        .title {
                            margin-left: 4rem;
                            font-weight: 400;
                            font-size: 1.6rem;
                            line-height: 150%;
                            color: $colorBlack;

                            @media (max-width: 768px) {
                                font-size: 2rem;
                            }

                            @media (max-width: 500px) {
                                font-size: 1.4rem;
                            }
                        }
                    }
                }
            }
        }
    }
}
