﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
    Layout = "MasterLayout.cshtml";
    var home = Model.Root();
    var pageTitle = Model.Value("pageTitle");
}

<div class="childpage-content  @Model.Name.ToLower().Replace(" ", "-").Replace("'", "-")">
    <div class="fluid-container">
        <section class="childpage-banner banner">
            @{
                Html.RenderPartial("Layout/Banner");
            }
        </section>
        <div class="inner-content">
            <div class="container-layout wow fadeInUpBig" data-wow-duration="1s">
                @*<div class="page-title">@pageTitle</div>*@
                @RenderBody()
            </div>
        </div>
    </div>
</div>