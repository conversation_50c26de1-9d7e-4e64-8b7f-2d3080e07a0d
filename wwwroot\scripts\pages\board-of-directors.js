$(document).ready(function () {
    $(".board-of-directors-container .head-info").click(function () {
        if ($(this).next(".desc").is(":visible")) {
            $(".board-of-directors-container .item").removeClass("active")
            $(this).parent(".item").removeClass("active")
            $(".desc").slideUp()
        }
        else {
            $(".board-of-directors-container .item").removeClass("active")
            $(this).parent(".item").addClass("active")
            $(".desc").slideUp()
            $(this).next(".desc").slideDown()

        }
    });
});