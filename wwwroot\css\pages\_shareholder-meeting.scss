.shareholder-section {
  .shareholder-content {
    .shareholder-item {
      margin-bottom: 5rem;

      @media (min-width: 320px) and (max-width: 767px) {
        margin-bottom: 3rem;
      }

      &:last-child {
        margin-bottom: 0;
      }

      .title-wrapper {
        border-bottom: 0.1rem solid rgba(13, 28, 33, 0.12);
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding-bottom: 1.5rem;

        .date {
          width: 15rem;

          p {
            font-style: normal;
            font-weight: 400;
            font-size: 1.4rem;
            line-height: 1.4;
            text-align: right;
            color: #0cb6f2;
            // font-family: "Sofia Pro";
            font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";

            @media (min-width: 767px) {
              font-size: 1.6rem;
            }
          }
        }

        .title {
          font-style: normal;
          font-weight: 400;
          //font-size: 2.4rem;
          line-height: 3.6rem;
          color: #0d1c21;
          width: calc(100% - 15rem);
          p {
            // font-family: "Sofia Pro";
            font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";

          }
        }
      }

      .shareholder {
        display: flex;
        flex-wrap: wrap;

        .shareholder-info {
          background: rgba(13, 28, 33, 0.04);
          width: calc(50% - 2rem);
          height: 6rem;
          margin-bottom: 2rem;
          border-bottom: 2px solid transparent;
          // transition: all 0.3s ease-in-out;
          position: relative;

          &:first-child {
            @media (min-width: 320px) and (max-width: 767px) {
              margin-top: 3rem;
            }
          }

          &:nth-child(odd) {
            margin-right: 4rem;

            @media (max-width: 992px) {
              margin-right: 1.5rem;
            }

            @media (min-width: 320px) and (max-width: 767px) {
              margin-right: 0;
            }
          }

          &:nth-child(-n + 2) {
            @media (min-width: 768px) {
              margin-top: 6rem;
            }
          }

          &:nth-last-child(-n + 2) {
            @media (min-width: 768px) {
              margin-bottom: 0;
            }
          }

          &:last-child {
            @media (min-width: 320px) and (max-width: 767px) {
              margin-bottom: 0;
            }
          }

          &:hover::after {
            width: 100%;
            left: 0;
          }

          &::after {
            position: absolute;
            content: "";
            width: 0%;
            height: 2px;
            background-color: #0cb6f2;
            left: 50%;
            bottom: 0;
            transition: all 0.3s ease-in-out;
          }

          @media (max-width: 992px) {
            width: calc(50% - 0.75rem);
          }

          @media (min-width: 320px) and (max-width: 767px) {
            width: 100%;
          }

          .shareholder-link {
            display: flex;
            align-items: center;
            height: 100%;

            img {
              padding-left: 5rem;
              padding-right: 4rem;
              // width: 2.8rem;
              height: 2.8rem;
              object-fit: cover;

              @media (max-width: 992px) {
                padding-left: 1.5rem;
                padding-right: 1.5rem;
              }
            }

            p {
              font-style: normal;
              font-weight: 400;
              font-size: 1.4rem;
              line-height: 2.4rem;
              color: #0d1c21;
              padding-right: 0.5rem;
              @media (min-width: 767px) {
                font-size: 1.6rem;
              }
            }
          }
        }
      }
    }
  }
}