// $(document).ready(function () {
//     var lang = $("html").attr("lang");
//     lang = lang.toLowerCase();
//     const containers = document.querySelectorAll('.esg-report-title');
//     var maxLength = 35;
//     if (lang === "en") {
//         maxLength = 70;
//     }
    
//     for (i = 0; i < containers.length; i++) {
//         var p = containers[i].querySelector('a');
//         if (p != null && p.textContent != null) {
//             var textLength = p.textContent.length;
//             p.textContent = p.textContent.substring(0, maxLength);
//             if (textLength > maxLength && lang === "en") {
//                 p.textContent = p.textContent.substring(0, p.textContent.lastIndexOf(' ')) + '...';
//             }
//             else if (textLength > maxLength && (lang === "cn" || lang === "hk")) {
//                 p.textContent = p.textContent + '...';
//             }
//         }
//     }
// }); 