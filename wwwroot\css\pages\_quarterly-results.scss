﻿.quarterly-results-container {
    .reports-table {
        display: flex;
        flex-wrap: nowrap;

        .left-title {
            width: 25%;

            @media screen and (max-width: 1061px) {
                width: calc(100% / 3);
            }

            .type {
                line-height: 150%;
                color: $colorBlack;
                height: 6rem;
                display: flex;
                justify-content: center;
                align-items: center;
                font-weight: 400;
                font-size: 1.4rem;
                line-height: 150%;
                width: 100%;
                @media (min-width: 767px) {
                    font-size: 1.6rem;
                }

                &:nth-child(2n + 1) {
                    background: rgba(13, 28, 33, 0.04);
                }

                &:first-child {
                    background: $colorBlue;
                }
            }
        }

        .right-content {
            width: 75%;
            position: relative;

            @media screen and (max-width: 1061px) {
                width: calc(100% - (100% / 3));
            }

            .quarterly-results-swiper-prev {
                width: 1.8rem;
                height: 2.8rem;
                margin-right: 5.5rem;
                position: absolute;
                top: 2rem;
                left: -1.8rem;
                z-index: 2;

                @media screen and (max-width: 600px) {
                    margin-right: 2.5rem;
                    left: 2rem;
                }

                &.swiper-button-disabled {
                    .left {
                        opacity: 0.6;
                    }
                }
            }

            .quarterly-results-swiper-next {
                width: 1.8rem;
                height: 2.8rem;
                position: absolute;
                top: 2rem;
                right: -1.8rem;
                z-index: 2;

                @media screen and (max-width: 1200px) {
                    right: 1rem;
                }

                &.swiper-button-disabled {
                    .right {
                        opacity: 0.6;
                    }
                }
            }

            .quarterly-results-swiper {
                overflow: hidden;

                .swiper-wrapper {
                    .swiper-slide {
                        @media screen and (max-width: 767px) {
                            height: unset !important;
                        }

                        .year {
                            width: 100%;
                            height: 6rem;
                            color: #ffffff;
                            background: $colorBlue;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            font-weight: 400;
                            font-size: 1.4rem;
                            line-height: 150%;
                            // font-family: "Sofia Pro";
                            font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
                            @media (min-width: 767px) {
                                font-size: 1.6rem;
                            }
                        }

                        .quaterly-results-year-content {
                            .row-data-content {
                                display: flex;
                                flex-wrap: nowrap;
                                justify-content: stretch;
                                align-items: center;
                                width: 100%;
                                height: 6rem;

                                &:nth-child(2n) {
                                    background: rgba(13, 28, 33, 0.04);
                                }

                                &:last-child {
                                    margin-bottom: unset;
                                }

                                .cell {
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    text-align: left;
                                    width: 22%;

                                    a {
                                        font-size: 1.4rem;
                                        line-height: 150%;
                                        color: rgba(13, 28, 33, 0.26);
                                        text-decoration: unset;
                                        cursor: text;
                                        @media (min-width: 767px) {
                                            font-size: 1.6rem;
                                        }
                                    }
                                }

                                .file-avaliable {
                                    // font-family: "Sofia Pro";
                                    font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
                                    a {
                                        color: $colorBlack;
                                        cursor: pointer;
                                        // font-family: "Sofia Pro";
                                        font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        .last-right {
            width: 12rem;

            @media screen and (max-width: 1200px) {
                display: none;
            }

            .right-sec {
                height: 6rem;
                width: 100%;

                &:nth-child(2n + 1) {
                    background: rgba(13, 28, 33, 0.04);
                }

                &:first-child {
                    background-color: $colorBlue;
                }
            }
        }
    }
}
