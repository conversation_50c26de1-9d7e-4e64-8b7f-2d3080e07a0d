﻿@using Newtonsoft.Json
@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor;
@{
    //get Json Url
    var home = Model.Root();
    var jsonUrl = home.Value("hPNewsUrl").ToString();
    var endDate = "&enddate=" + DateTime.Today.ToString("yyyy-MM-dd");
    jsonUrl = jsonUrl + endDate;
    List<News> listNews = new List<News>();
    //call controller get list Object
    if (CMSCommon.Controllers.CMSCommon.GetNewsFromJson(jsonUrl)!=null)
    {
        listNews = CMSCommon.Controllers.CMSCommon.GetNewsFromJson(jsonUrl);
    }

    //get group year of News
    List<int> listYear = new List<int>();
    foreach (var item in listNews)
    {
        var yearOfNews = item.PublishedDate.Year;
        if (yearOfNews > 0)
        {
            listYear.Add(yearOfNews);
        }
    }
    var groupYear = listYear.Distinct();

    var dateText = Umbraco.GetDictionaryValue("Date", "Date");
    var titleText = Umbraco.GetDictionaryValue("Title and Summary", "Title and Summary");
    var searchText = Umbraco.GetDictionaryValue("Search", "Search");
    var submitText = Umbraco.GetDictionaryValue("Submit", "Submit");
    var allYearText = Umbraco.GetDictionaryValue("All", "All");
    var resultText = Umbraco.GetDictionaryValue("Results", "Results");

    var searchAction = "false";
    if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Query["searchAction"]))
    {
        searchAction = HttpContextAccessor.HttpContext.Request.Query["searchAction"];
    }
    var searchString = "";
    if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Query["keyword"]))
    {
        searchString = HttpContextAccessor.HttpContext.Request.Query["keyword"];
    }
    var yearTab = "";
    if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Query["yearTab"]))
    {
        yearTab = HttpContextAccessor.HttpContext.Request.Query["yearTab"];
    }
    var searchResults = listNews;
    foreach (var news in listNews)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            searchResults = searchResults.Where(x => (x.Title.ToLower().Contains(searchString.ToLower())))
                                            .OrderByDescending(x => x.PublishedDate).ToList<News>();
        }
        
    }
    var pageSize = 10;
    var page = 1;
    if(!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Query["page"]))
    {
        int.TryParse(HttpContextAccessor.HttpContext.Request.Query["page"], out page);
    }

    var totalPages = (int)Math.Ceiling((double)listNews.Count() / (double)pageSize);

    if (page > totalPages)
    {
        page = totalPages;
    }
    else if (page < 1)
    {
        page = 1;
    }
}
<div class="news-release-section">
    <div class="news-release-wrapper">
        <div class="search">
            <form>
                <input id="key-word" type="text" class="search-box" name="search" placeholder="@searchText">
                <input type="submit" class="submit-btn" value="@submitText">
            </form>
        </div>
        @if (listNews.Count()>0)
        {
            <div class="year-select">
                <ul class="year-list">
                    
                        <li id="all-news" class="year-item @( (yearTab.Equals("") ) || (yearTab.Equals("all-year"))? "active" : "")">
                            @allYearText 
                        </li>

                        @if (groupYear != null && listYear.Count > 0)
                        {
                            foreach (var year in groupYear)
                            {

                                <li class="year-item @(yearTab.Equals(year.ToString()) == true ? "active" : "")">@year.ToString().Trim()</li>
                            }
                        }
                    
                    @if (searchAction.Equals("false") == false)
                    {
                        // Result tab
                            <li id="results" class="year-item @(yearTab.Equals("results")? "active" : "")">
                                @resultText
                            </li>
                    }
                </ul>
        </div>
        }
    </div>
    @if(listNews.Count()>0){
        <div class="news-header">
            <div class="date-title ">
                <p>@dateText</p>
            </div>

            <div class="summary-title">
                <p>@titleText</p>
            </div>
        </div>
        <div class="news-contents">
            <div id="all-year-content"
             class="year-content @( (yearTab.Equals("") ) || (yearTab.Equals("all-year"))? "show" : "")">

                @{
                    if (yearTab.Equals("all-year") == false)
                    {
                        page = 1;
                    }
                    else if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Query["page"]))
                    {
                        Int32.TryParse(HttpContextAccessor.HttpContext.Request.Query["page"], out page);
                    }
                    listItems(listNews.Skip((page - 1) * pageSize).Take(pageSize).ToList());
                    pagination(page, totalPages, "all-year", searchString, "false");
                }

            </div>
            @foreach (var year in groupYear)
            {
                var yearContent = listNews.Where(x => ((x.PublishedDate.Year) == year))
                .OrderByDescending(x => x.PublishedDate)
                .ToList();
                totalPages = (int)Math.Ceiling((double)yearContent.Count() / (double)pageSize);

                if (yearTab.Equals(year.ToString()) == false)
                {
                    page = 1;
                }
                else if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Query["page"]))
                {
                    Int32.TryParse(HttpContextAccessor.HttpContext.Request.Query["page"], out page);
                }

                <div id="@year-content"
             class="year-content @((yearTab.Equals(year.ToString()))? "show" : "")">
                    @{
                        listItems(yearContent.Skip((page - 1) * pageSize).Take(pageSize).ToList());
                        pagination(page, totalPages, year.ToString(), searchString, "false");
                    }
                </div>
            }
            @if (searchResults != null && searchResults.Any())
            {

                <div id="search-results"
             class="year-content @(yearTab.Equals("results")==true ? "show" : "")">
                    @{
                        totalPages = (int)Math.Ceiling((double)searchResults.Count() / (double)pageSize);
                        if (yearTab.Equals("results") == false)
                        {
                            page = 1;
                        }
                        else if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Query["page"]))
                        {
                            Int32.TryParse(HttpContextAccessor.HttpContext.Request.Query["page"], out page);
                        }
                    }

                    @{
                        listItems(searchResults.Skip((page - 1) * pageSize).Take(pageSize).ToList());
                        pagination(page, totalPages, "results", searchString, "true");
                    }

                </div>

            }
        </div>
    }
</div>
@functions {
    private void pagination(int page, int totalPages, string yearTab, string keyword, string searchAction)
    {
        var txtNext = Umbraco.GetDictionaryValue("Next", "next").ToString();
        var txtPrev = Umbraco.GetDictionaryValue("Previous", "Prev").ToString();
        <div class="pagination fy">
                @if (searchAction.Equals("false") == true)
            {
                if (page > 1)
                {
                    var href = string.Format("?yearTab={0}&page={1}&searchAction={2}", yearTab, (page - 1), "false");
                        <a href="@href" class="prev" aria-label="@txtPrev">@txtPrev</a>
                }
                if (totalPages >=1)
                {
                    for (int p = 1; p < totalPages + 1; p++)
                    {
                        var href = string.Format("?yearTab={0}&page={1}&searchAction={2}", yearTab, @p, "false");
                            <a class="@(p == page ? "active" : string.Empty)" href="@href">@p</a>
                    }
                }
                
                if (page < totalPages)
                {
                    var href = string.Format("?yearTab={0}&page={1}&searchAction={2}", yearTab, (page + 1),"false");
                        <a href="@href" class="next" aria-label="@txtNext">@txtNext</a>
                }
            }
            else
            {
                if (page > 1)
                {
                    var href = string.Format("?yearTab={0}&page={1}&searchAction={2}&keyword={3}", yearTab, (page - 1), "true", keyword);
                    <a href="@href" class="prev" aria-label="@txtPrev">@txtPrev</a>
                }
                if (totalPages >= 1)
                {
                    for (int p = 1; p < totalPages + 1; p++)
                    {
                        var href = string.Format("?yearTab={0}&page={1}&searchAction={2}&keyword={3}", yearTab, @p, "true",keyword);
                        <a class="@(p == page ? "active" : string.Empty)" href="@href">@p</a>
                    }
                }
                if (page < totalPages)
                {
                    var href = string.Format("?yearTab={0}&page={1}&searchAction={2}&keyword={3}", yearTab, (page + 1), "true", keyword);
                    <a href="@href" class="next" aria-label="@txtNext">@txtNext</a>
                }
            }
    </div>
    }
}
@functions {
    private void listItems(List<News> yearContent)
    {
    @foreach (var yearItem in yearContent)
        {

            var title = yearItem.Title;
            var url = yearItem.Url.Replace("%26", "&");
            var publishedDate = yearItem.PublishedDate;
            var date = publishedDate.ToString(@Umbraco.GetDictionaryValue("Date Format - MM/dd/yyyy"));

    <div class="news-item">
        <div class="date">
            <p>@date</p>
        </div>
        <div class="title">
            <a onclick="window.open('@url', '_blank', 'location=yes,height=600,width=600,scrollbars=yes,status=yes');">
                @title
            </a>
        </div>
    </div>
        }
    }
}