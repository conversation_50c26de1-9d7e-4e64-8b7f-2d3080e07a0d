﻿.board-of-directors-container {
    .item {
        background: rgba(13, 28, 33, 0.04);
        margin-bottom: 4rem;
        padding: 2.5rem 8rem;

        @media screen and (max-width: 767px) {
            padding: 2.5rem;
        }

        .head-info {
            position: relative;
            cursor: pointer;

            @media (min-width: 320px) and (max-width: 767px) {
                padding-right: 1.8rem;
            }

            .person-info {
                .name {
                    font-weight: 400;
                    font-size: 1.4rem;
                    line-height: 150%;
                    color: $colorBlue;
                    margin-bottom: 1rem;
                    @media (min-width: 768px) and (max-width: 1280px) {
                        font-size: 1.8rem;
                    }

                    @media (min-width: 1281px) {
                        font-size: 2.4rem;
                    }

                    @media (max-width: 600px) {
                        font-size: 1.8rem !important;
                    }
                }

                .position {
                    font-weight: 400;
                    font-size: 1.4rem;
                    line-height: 150%;
                    color: $colorBlack;
                    margin-bottom: 2rem;
                    @media (min-width: 768px) {
                        font-size: 1.6rem;
                    }
                }
            }

            .arrow {
                position: absolute;
                top: 1.5rem;
                right: -4rem;

                @media screen and (max-width: 767px) {
                    top: 0;
                    right: -17px;
                    display: flex;
                    flex-direction: column;
                    height: 100%;
                    justify-content: center;
                }

                img {
                    width: 4rem;
                    height: 4rem;
                    transform: rotate(180deg);

                    @media screen and (max-width: 767px) {
                        width: 3rem;
                        height: 3rem;
                    }
                }
            }
        }

        .desc {
            font-weight: 400;
            font-size: 1.6rem;
            line-height: 150%;
            color: $colorGrey;
            display: none;

            @media (min-width: 320px) and (max-width: 480px) {
                font-size: 1.4rem;
            }
        }
    }

    .item.active {
        .head-info {
            .arrow {
                img {
                    transform: revert;
                }
            }
        }
    }
}
