﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;

@{
    var quickLinkList = Model.Root().Value<IEnumerable<IPublishedElement>>("quickLinkList").ToList();
        var discoverMoreLink = (Model.HasValue("discoverMoreLink") && Model.Value<Link>("discoverMoreLink") != null) ?
    Model.Value<Link>("discoverMoreLink").Url : "";
}
<div class="ocean">
    <div class="wave">
    </div>
    <div class="container">
        <div class="discover-more-link">
            @* <a href="@discoverMoreLink" class="font-bold-pro" target="_blank">
                @Umbraco.GetDictionaryValue("Discover more")
            </a> *@

            <p class="font-bold-pro" style="cursor: default;">
                @Umbraco.GetDictionaryValue("Discover more")
            </p>
        </div>
        <div class="quicklinks__list">
            @if (quickLinkList != null && quickLinkList.Any())
            {
                foreach (var item in quickLinkList)
                {
                    var title = item.Value("title");
                    var photo = item.Value<IPublishedContent>("photo")?.Url() ?? "";
                    var linkUrl = (item.HasValue("linkUrl") && item.Value<Link>("linkUrl") != null) ?
                    item.Value<Link>("linkUrl").Url : "";
                    <div class="ql__item">
                        <a href="@linkUrl" target="_blank" class="ql__link">
                            <div class="ql__icon">
                                <img src="@photo" alt="@title ">
                            </div>
                            <div class="ql__title">
                                @title
                            </div>
                        </a>
                    </div>
                }
            }
        </div>
    </div>
</div>