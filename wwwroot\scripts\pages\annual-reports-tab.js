function initializeAnnualInterimReports() {
    const container = document.querySelector(".annual-interim-reports-container");
    if (!container) return;

    const tabs = container.querySelectorAll(".tab-link");
    const contents = container.querySelectorAll(".tab-contents");

    function getParentTabContext() {
        // Get clean hash without query parameters
        let hash = window.location.hash;
        if (hash && hash.includes('?')) {
            hash = hash.substring(0, hash.indexOf('?'));
        }

        if (hash) {
            return hash;
        }

        const activeParentTab = document.querySelector('.nav-link.active[href^="#"]:not(.annual-interim-reports-container .nav-link)');
        if (activeParentTab) {
            return activeParentTab.getAttribute('href');
        }

        const parentContext = container?.getAttribute('data-parent-tab') || '';
        if (parentContext) {
            return parentContext.startsWith('#') ? parentContext : '#' + parentContext;
        }

        return '';
    }

    tabs.forEach(tab => {
        tab.addEventListener("click", function (e) {
            e.preventDefault();

            const slug = this.dataset.slug;
            const target = this.dataset.tab;

            const parentContext = getParentTabContext();
            const newUrl = window.location.pathname + parentContext + "?tab=" + slug;

            console.log('Updating URL to:', newUrl);
            history.pushState({}, "", newUrl);

            tabs.forEach(t => t.classList.remove("active"));
            contents.forEach(c => c.classList.remove("active"));

            this.classList.add("active");
            const targetElement = document.getElementById(target);
            if (targetElement) {
                targetElement.classList.add("active");
            }
        });
    });

    function parseUrl() {
        let tabParam = '';

        // Check for format: #hash?tab=value
        if (window.location.hash && window.location.hash.includes('?')) {
            const hashPart = window.location.hash;
            const questionIndex = hashPart.indexOf('?');
            const queryPart = hashPart.substring(questionIndex + 1);
            const params = new URLSearchParams(queryPart);
            tabParam = params.get('tab') || '';
        }
        // Check for standard format: ?tab=value#hash
        else {
            const urlParams = new URLSearchParams(window.location.search);
            tabParam = urlParams.get('tab') || '';
        }

        return tabParam;
    }

    function initializeFromUrl() {
        const tabParam = parseUrl();

        if (tabParam) {
            const targetTab = container.querySelector(`.tab-link[data-slug="${tabParam}"]`);

            if (targetTab) {
                const targetId = targetTab.dataset.tab;
                const targetContent = document.getElementById(targetId);

                if (targetContent) {
                    tabs.forEach(t => t.classList.remove("active"));
                    contents.forEach(c => c.classList.remove("active"));

                    targetTab.classList.add("active");
                    targetContent.classList.add("active");
                }
            }
        }
    }

    setTimeout(initializeFromUrl, 100);

    window.addEventListener('popstate', function() {
        console.log('Popstate event, reinitializing');
        initializeFromUrl();
    });

    window.addEventListener('hashchange', function() {
        console.log('Hash changed, checking if we need to reinitialize');

        // Check if hash contains annual-and-interim-reports and has tab parameter
        const hash = window.location.hash;
        if (hash.includes('annual-and-interim-reports') && parseUrl()) {
            // Small delay to let other scripts finish
            setTimeout(function() {
                initializeFromUrl();
            }, 50);
        }
    });
}

// Initialize on DOM ready
document.addEventListener("DOMContentLoaded", function () {
    initializeAnnualInterimReports();
});

// Global function to re-initialize when needed
window.reinitializeAnnualInterimReports = function() {
    initializeAnnualInterimReports();
};
