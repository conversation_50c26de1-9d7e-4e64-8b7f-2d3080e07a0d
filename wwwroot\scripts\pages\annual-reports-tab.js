$(document).ready(function () {
     document.addEventListener("DOMContentLoaded", function () {
        const container = document.querySelector(".annual-interim-reports-container");
        if (!container) return;

        const tabs = container.querySelectorAll(".tab-link");
        const contents = container.querySelectorAll(".tab-contents");

        function getParentTabContext() {
            if (window.location.hash) {
                return window.location.hash;
            }

            const activeParentTab = document.querySelector('.nav-link.active[href^="#"]:not(.annual-interim-reports-container .nav-link)');
            if (activeParentTab) {
                return activeParentTab.getAttribute('href');
            }

            const parentContext = container?.getAttribute('data-parent-tab') || '';
            if (parentContext) {
                return parentContext.startsWith('#') ? parentContext : '#' + parentContext;
            }

            return '';
        }

        tabs.forEach(tab => {
            tab.addEventListener("click", function (e) {
                e.preventDefault();

                const slug = this.dataset.slug;
                const target = this.dataset.tab;

                const parentContext = getParentTabContext();
                const newUrl = window.location.pathname + "?tab=" + slug + parentContext;

                console.log('Updating URL to:', newUrl);
                history.pushState({}, "", newUrl);

                tabs.forEach(t => t.classList.remove("active"));
                contents.forEach(c => c.classList.remove("active"));

                this.classList.add("active");
                const targetElement = document.getElementById(target);
                if (targetElement) {
                    targetElement.classList.add("active");
                }
            });
        });

        function initializeFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const tabParam = urlParams.get('tab');

            console.log('Initializing from URL, tab param:', tabParam);

            if (tabParam) {
                const targetTab = container.querySelector(`.tab-link[data-slug="${tabParam}"]`);

                if (targetTab) {
                    const targetId = targetTab.dataset.tab;
                    const targetContent = document.getElementById(targetId);

                    console.log('Found target tab:', targetTab, 'target content:', targetContent);

                    if (targetContent) {
                        tabs.forEach(t => t.classList.remove("active"));
                        contents.forEach(c => c.classList.remove("active"));

                        targetTab.classList.add("active");
                        targetContent.classList.add("active");
                    }
                }
            }
        }

        setTimeout(initializeFromUrl, 100);

        window.addEventListener('popstate', function() {
            console.log('Popstate event, reinitializing');
            initializeFromUrl();
        });

        window.addEventListener('hashchange', function() {
            console.log('Hash changed, checking if we need to reinitialize');
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('tab')) {
                initializeFromUrl();
            }
        });
    });
});