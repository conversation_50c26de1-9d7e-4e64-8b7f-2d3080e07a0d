/**
 * Dynamic Tabs Handler
 * Handles nested tab functionality with query parameters and dynamic parent context
 */

class DynamicTabsHandler {
    constructor(containerSelector) {
        this.containerSelector = containerSelector;
        this.container = document.querySelector(containerSelector);
        if (!this.container) {
            console.warn(`Dynamic tabs container not found: ${containerSelector}`);
            return;
        }
        
        this.tabs = this.container.querySelectorAll(".tab-link");
        this.contents = this.container.querySelectorAll(".tab-contents");
        
        this.init();
    }

    init() {
        this.bindEvents();
        // Small delay to ensure all elements are loaded
        setTimeout(() => this.initializeFromUrl(), 100);
    }

    parseUrl() {
        let hash = '';
        let tabParam = '';

        // Check for new format: #hash?tab=value
        if (window.location.hash && window.location.hash.includes('?')) {
            const hashPart = window.location.hash;
            const questionIndex = hashPart.indexOf('?');
            hash = hashPart.substring(0, questionIndex);
            const queryPart = hashPart.substring(questionIndex + 1);
            const params = new URLSearchParams(queryPart);
            tabParam = params.get('tab') || '';
        }
        // Check for standard format: ?tab=value#hash
        else {
            hash = window.location.hash;
            const urlParams = new URLSearchParams(window.location.search);
            tabParam = urlParams.get('tab') || '';
        }

        console.log('Parsed URL - Hash:', hash, 'Tab:', tabParam);
        return { hash, tabParam };
    }

    bindEvents() {
        // Handle tab clicks
        this.tabs.forEach(tab => {
            tab.addEventListener("click", (e) => this.handleTabClick(e));
        });

        // Handle browser back/forward
        window.addEventListener('popstate', () => {
            console.log('Popstate event, reinitializing');
            this.initializeFromUrl();
        });

        // Handle hash changes - this is key for your issue
        window.addEventListener('hashchange', () => {
            console.log('Hash changed, checking if we need to reinitialize');
            this.handleHashChange();
        });
    }

    handleHashChange() {
        const { hash, tabParam } = this.parseUrl();

        console.log('Hash changed to:', hash, 'Tab param:', tabParam, 'Container:', this.containerSelector);

        // Check if this hash change is relevant to our container
        if (this.isRelevantHashChange(hash)) {
            console.log('Hash change is relevant, reinitializing tabs for:', this.containerSelector);
            this.initializeFromUrl();
        } else {
            console.log('Hash change not relevant for:', this.containerSelector);
        }
    }

    isRelevantHashChange(hash) {
        // Check if the hash corresponds to a parent context that should show our container
        const parentContext = this.container?.getAttribute('data-parent-tab') || '';

        // If our container's parent context matches the new hash
        if (parentContext === hash) {
            return true;
        }

        // Check if hash contains keywords related to our container
        const containerClass = this.container.className;

        // More flexible matching for annual-interim-reports
        if (containerClass.includes('annual-interim-reports')) {
            if (hash.includes('annual-and-interim-reports') ||
                hash.includes('annual-interim-reports') ||
                hash === '#annual-and-interim-reports' ||
                hash === '#annual-interim-reports') {
                return true;
            }
        }

        // More flexible matching for annual-reports
        if (containerClass.includes('annual-reports') && !containerClass.includes('interim')) {
            if (hash.includes('annual-reports') && !hash.includes('interim')) {
                return true;
            }
        }

        console.log('Hash not relevant:', hash, 'for container:', containerClass);
        return false;
    }

    handleTabClick(e) {
        e.preventDefault();

        const tab = e.currentTarget;
        const slug = tab.dataset.slug;
        const target = tab.dataset.tab;

        const parentContext = this.getParentTabContext();
        // New URL format: #hash?tab=slug
        const newUrl = window.location.pathname + parentContext + "?tab=" + slug;

        console.log('Updating URL to:', newUrl);
        history.pushState({}, "", newUrl);

        this.activateTab(tab, target);
    }

    getParentTabContext() {
        // Method 1: Check current hash
        if (window.location.hash) {
            return window.location.hash;
        }
        
        // Method 2: Check for active parent tab link (exclude current container)
        const containerClass = this.container.className.split(' ')[0];
        const activeParentTab = document.querySelector(`.nav-link.active[href^="#"]:not(.${containerClass} .nav-link)`);
        if (activeParentTab) {
            return activeParentTab.getAttribute('href');
        }
        
        // Method 3: Try to detect from page context or data attributes
        const parentContext = this.container?.getAttribute('data-parent-tab') || '';
        if (parentContext) {
            return parentContext.startsWith('#') ? parentContext : '#' + parentContext;
        }
        
        // Method 4: Try to detect from URL or page structure
        const pathSegments = window.location.pathname.split('/').filter(s => s);
        const lastSegment = pathSegments[pathSegments.length - 1];
        if (lastSegment && lastSegment !== 'financial-information') {
            return '#' + lastSegment;
        }
        
        return '';
    }

    activateTab(activeTab, targetId) {
        // Reset all tabs
        this.tabs.forEach(t => t.classList.remove("active"));
        this.contents.forEach(c => c.classList.remove("active"));

        // Activate target tab
        activeTab.classList.add("active");
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
            targetElement.classList.add("active");
        }
    }

    initializeFromUrl() {
        const { hash, tabParam } = this.parseUrl();

        console.log('Initializing from URL, tab param:', tabParam, 'hash:', hash);

        if (tabParam) {
            const targetTab = this.container.querySelector(`.tab-link[data-slug="${tabParam}"]`);

            if (targetTab) {
                const targetId = targetTab.dataset.tab;
                const targetContent = document.getElementById(targetId);

                console.log('Found target tab:', targetTab, 'target content:', targetContent);

                if (targetContent) {
                    this.activateTab(targetTab, targetId);
                }
            } else {
                console.log('Tab not found for slug:', tabParam);
            }
        } else {
            // No tab parameter, activate first tab if container is visible
            if (this.isContainerVisible()) {
                const firstTab = this.tabs[0];
                const firstContent = this.contents[0];
                if (firstTab && firstContent) {
                    this.activateTab(firstTab, firstContent.id);
                }
            }
        }
    }

    isContainerVisible() {
        // Check if the container should be visible based on current hash
        const currentHash = window.location.hash;
        return this.isRelevantHashChange(currentHash);
    }
}

// Global registry to track all tab handlers
window.dynamicTabHandlers = window.dynamicTabHandlers || [];

// Auto-initialize when DOM is ready
document.addEventListener("DOMContentLoaded", function () {
    // Initialize for Annual Reports
    if (document.querySelector(".annual-reports-container")) {
        const handler = new DynamicTabsHandler(".annual-reports-container");
        window.dynamicTabHandlers.push(handler);
    }
    
    // Initialize for Annual & Interim Reports
    if (document.querySelector(".annual-interim-reports-container")) {
        const handler = new DynamicTabsHandler(".annual-interim-reports-container");
        window.dynamicTabHandlers.push(handler);
    }
    
    // Initialize for any container with data-dynamic-tabs attribute
    document.querySelectorAll('[data-dynamic-tabs="true"]').forEach(container => {
        const containerClass = '.' + container.className.split(' ')[0];
        const handler = new DynamicTabsHandler(containerClass);
        window.dynamicTabHandlers.push(handler);
    });
});

// Export for manual initialization if needed
window.DynamicTabsHandler = DynamicTabsHandler;
