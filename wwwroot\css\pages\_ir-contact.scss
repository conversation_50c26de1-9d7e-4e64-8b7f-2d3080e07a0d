﻿.ir-contact-container {
    .location-wrap {
        margin-bottom: 6rem;
    }

    .location {
        height: 6rem;
        background: $colorBlue;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 700;
        font-size: 1.6rem;
        line-height: 150%;
        color: #ffffff;
        margin-bottom: 2rem;
    }

    .offices {
        display: flex;
        justify-content: space-evenly;
        gap: 2rem;

        @media screen and (max-width: 480px) {
            flex-wrap: wrap;
            justify-content: flex-start;
            gap: 3rem;
        }

        .item {
            .location-name {
                font-weight: 400;
                font-size: 1.6rem;
                line-height: 150%;
                color: $colorBlack;
                margin-bottom: 1rem;
                // font-family: "Sofia Pro";
                font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
            }

            .person-name {
                font-weight: 400;
                // font-size: 2.4rem;
                line-height: 1.5;
                color: $colorBlack;
                margin-bottom: 1rem;
                // font-family: "Sofia Pro";
                font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
            }

            .phone-number,
            .email {
                display: flex;
                justify-content: flex-start;
                align-items: center;

                img {
                    margin-right: 1rem;
                }
            }

            .phone-number {
                margin-bottom: 0.8rem;
                font-weight: 400;
                font-size: 1.6rem;
                line-height: 150%;
                //color: rgba(13, 28, 33, 0.54);
                //color: #0cb6f2;
                a {
                    color: #0cb6f2;
                    // font-family: "Sofia Pro";
                    font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
                }
            }

            .email {
                a {
                    font-weight: 400;
                    font-size: 1.6rem;
                    line-height: 150%;
                    text-decoration-line: underline;
                    color: $colorBlue;
                    // font-family: "Sofia Pro";
                    font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
                }
            }
        }
    }
}
