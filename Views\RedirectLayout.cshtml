﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage

@{ 
    Layout = null;
    var home = Model.Root();
    var redirectLink = Model.Value<Link>("redirectLink");
    var nullUrl = "javascript:void(0)"; }

@if (redirectLink != null && redirectLink.Url.Length > 0)
{
    Response.Redirect(redirectLink.Url);
}
else if (redirectLink == null)
{
    Response.Redirect(nullUrl);
}