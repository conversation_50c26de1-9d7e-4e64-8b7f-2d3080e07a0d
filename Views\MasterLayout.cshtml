﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;

@{
    var language = System.Threading.Thread.CurrentThread.CurrentCulture.Name;
    var home = Model.Root();
    var hPNewsUrl = home.Value<string>("hPNewsUrl");
    if (language.Equals("en-US"))
    {
        language = "en";
    }
    if (language.Equals("zh-Hans-CN"))
    {
        language = "cn";
    }
    if (language.Equals("zh-Hant-HK"))
    {
        language = "hk";
    }
    var dir = language.Equals("ar-SA") ? "dir=rtl" : "dir=ltr";

    var iconTop = "/media/ny1bu2ts/microsoftteams-image-44.png";

    var pageTitle = Model.Value<string>("pageTitle");
    string imageQR = home.Value<IPublishedContent>("imageQR")?.Url() ?? "";
}


<!doctype html>
<!--[if gt IE 8]><!-->
<html class="no-js" lang="@language" @dir>
<!--<![endif]-->

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0">

    <meta name="description" content="@home.Value("seoMetaDescription")">
    <meta name="keywords" content="@string.Join(", ", home.Value<String[]>("keywords"))">
    <meta name="msvalidate.01" content="">
    <title>@Html.Raw(pageTitle)</title>

    @Html.Raw(await runtimeMinifier.RenderCssHereAsync("inline-css-bundle"))

    @await Umbraco.RenderMacroAsync("GoogleTagManager", new { containerId = "G-HE7QWR90TV" })
</head>

<body>

    <script>
        var hPNewsUrl = "@Html.Raw(hPNewsUrl)";
    </script>
    @await Umbraco.RenderMacroAsync("GoogleTagManagerNoScript", new { containerId = "G-HE7QWR90TV" })
    <div class="background-opacity"></div>
    <header class="site-header">
        @{
            Html.RenderPartial("Layout/Header");
        }
    </header>
    <main class="site-content">
        @RenderBody()

        <div class="back-to-top">
            <div class="icon-image">
                <img src="@iconTop" />
            </div>
        </div>
    </main>
    <div class="image-qr-mobile">
        <img class="qr-image" src="@imageQR" alt="@Umbraco.GetDictionaryValue("QR Code")">
    </div>
    <footer class="site-footer">
        @{
            Html.RenderPartial("Layout/Footer");
        }
    </footer>
    <script type="text/javascript"
        src="//asia.tools.euroland.com/tools/common/eurolandiframeautoheight/eurolandtoolsintegrationobject.js">
        </script>
    @Html.Raw(await runtimeMinifier.RenderJsHereAsync("inline-js-bundle3"))
</body>

</html>
