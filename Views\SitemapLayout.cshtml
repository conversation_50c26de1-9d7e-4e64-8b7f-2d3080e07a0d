﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
  Layout = "ChildPageLayout.cshtml";
}
@{
  var pageTitle = Model.Value<string>("pageTitle");
}

<div class="sitemap-container">
  <div class="container ">
    @{
      var selection = Umbraco.ContentAtRoot().FirstOrDefault()
      .Children().Where(x => x.IsVisible());
      int count = 0;
    }

    <ul class="bread-crumb clearfix">
      @foreach (var item in selection)
      {
        count++;
        if (item.Level <= 4)
        {
          <li class="<EMAIL>">
            <a href="@item.Url()" title="">
              <span>
                @item.Value("pageTitle")
              </span>
            </a>
            @{
              var children = item.Children().Where(x => x.IsVisible());
              if (children.Any())
              {
                <ul>
                  @foreach (var itemChild in children)
                  {
                    <li class="<EMAIL>">
                      <a href="@itemChild.Url()" title="">
                        <span>
                          @itemChild.Value("pageTitle")
                        </span>
                        @{
                          generateMenu(itemChild.Children().Where(x => x.IsVisible()));
                        }
                      </a>
                    </li>
                  }
                </ul>
              }
            }
          </li>
        }
      }
    </ul>
  </div>
</div>

@functions {
  private void generateMenu(IEnumerable<IPublishedContent> menuItems)
  {
    <ul>
      @foreach (var item in menuItems)
      {
        int count = 0;
        count++;
        if (item.Level <= 3) 
        {
          <li class="<EMAIL>">
            <a href="@item.Url()" title="">
              <span>
                @item.Value("pageTitle")
              </span>
            </a>
          </li>
        }
      }
    </ul>
  }
}