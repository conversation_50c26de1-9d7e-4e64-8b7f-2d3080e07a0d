@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;
@using System.Text.RegularExpressions;
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor;

@{
  var home = Model.Root();
  var newsReleasePage = home.DescendantsOfType("newsReleasePage")?.FirstOrDefault() ?? null;
  var newsContent = newsReleasePage.Children()
  .Where(x => x.IsVisible())
  .OrderByDescending(x => x.Value<DateTime>("publishedDate"))
  .ThenByDescending(x => x.SortOrder).ToList();

  var tabs = Model.Children().OrderByDescending(x => x.Value("sortOrder")).Take(1);

  var dateText = Umbraco.GetDictionaryValue("Date", "Date");
  var titleText = Umbraco.GetDictionaryValue("Title and Summary", "Title and Summary");
  var searchText = Umbraco.GetDictionaryValue("Search", "Search");
  var submitText = Umbraco.GetDictionaryValue("Submit", "Submit");
  var allYearText = Umbraco.GetDictionaryValue("All", "All");
  var resultText = Umbraco.GetDictionaryValue("Result", "Result");

  List<int> listYear = new List<int>();
  foreach (var item in newsContent)
  {
    var year = 0;
    if (item.Value<int>("year") > 0)
    {
      year = item.Value<int>("year");
      listYear.Add(year);
    }
  }
  var groupYear = listYear.Distinct();

  var searchString = "";
  if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Query["keyword"]))
  {
    searchString = HttpContextAccessor.HttpContext.Request.Query["keyword"];
  }

  var tabActive = "";
  if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Query["tab"]))
  {
    tabActive = HttpContextAccessor.HttpContext.Request.Query["tab"];
  }

  var yearTab = "";
  if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Query["yearTab"]))
  {
    yearTab = HttpContextAccessor.HttpContext.Request.Query["yearTab"];
  }

  var searchResults = new List<IPublishedContent>();
  var searchAction = "false";
  if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Query["searchAction"]))
  {
    searchAction = HttpContextAccessor.HttpContext.Request.Query["searchAction"];
  }

  if (!string.IsNullOrEmpty(searchString))
  {
    searchResults = newsReleasePage.Children()
    .Where(x => (x.Value<String>("title").ToLower()
    .Contains(searchString.ToLower())))
    .OrderByDescending(x => x.Value("publishedDate")).ToList();
  }


  var pageSize = 2;
  var page = 1;
  int.TryParse(HttpContextAccessor.HttpContext.Request.Query["page"], out page);

  var totalPages = (int)Math.Ceiling((double)newsContent.Count() / (double)pageSize);

  if (page > totalPages)
  {
    page = totalPages;
  }
  else if (page < 1)
  {
    page = 1;
  }
}

<div class="news-release-section">
  <div class="news-release-wrapper">
    <div class="search">
      <form>
        <input id="key-word" type="text" class="search-box" name="search" placeholder="@searchText">
        <input type="submit" class="submit-btn" value="@submitText">
      </form>
    </div>

    <div class="year-select">
      <div class="tab-head">
        <ul class="nav" role="tablist">
          @if (searchAction.Equals("false") == true)
          {
            <li id="all-news" class="nav-item year-item " role="presentation">
              <a class="nav-link @( (yearTab.Equals("") ) || (yearTab.Equals("all-year"))? "active" : "")" data-toggle=tab
              href="#all-year-content" aria-expanded="true">
                @allYearText
              </a>
            </li>

            @if (listYear != null && listYear.Count > 0)
            {
              foreach (var year in groupYear)
              {
                var href = "#" + year;
                var isTab = true;
                var dataToggle = isTab ? "data-toggle=tab" : "";

                <li class="nav-item year-item" role="presentation">
                  <a class="nav-link" @dataToggle href="@href" aria-expanded="true">
                    @year.ToString().Trim()
                  </a>
                </li>
              }
            }
          }
          else
          {
            <li id="all-news" class="nav-item year-item " role="presentation">
              <a class="nav-link @( (yearTab.Equals("") ) || (yearTab.Equals("all-year"))? "active" : "")" data-toggle=tab
              href="#all-year-content" aria-expanded="true">
                @allYearText
              </a>
            </li>

            @if (listYear != null && listYear.Count > 0)
            {
              foreach (var year in groupYear)
              {
                var href = "#" + year;
                var isTab = true;
                var dataToggle = isTab ? "data-toggle=tab" : "";

                <li class="nav-item year-item" role="presentation">
                  <a class="nav-link" @dataToggle href="@href" aria-expanded="true">
                    @year.ToString().Trim()
                  </a>
                </li>
              }
            }

            // Result tab
            <li id="result" class="nav-item year-item " role="presentation">
              <a class="nav-link @( (yearTab.Equals("") ) || (yearTab.Equals("results"))? "active" : "")"
              data-toggle=tab href="#result-content" aria-expanded="true">
                @resultText
              </a>
            </li>

            // Result text
            <div class="search-result-report">
              <div class="report-data">@Umbraco.GetDictionaryValue("Total", "Total"): 
                @searchResults.Count()
                @Umbraco.GetDictionaryValue("releases", "release")
              </div>
            </div>
          }
        </ul>
      </div>
    </div>
  </div>

  <div class="tab-contents">
    <div class="news-header">
      <div class="date-title ">
        <p>@dateText</p>
      </div>

      <div class="summary-title">
        <p>@titleText</p>
      </div>
    </div>

    <div class="news-content">
      @if (searchAction.Equals("false") == true)
      {

        <div id="all-year-content"
        class="news-item tab-pane fade year-content @( (yearTab.Equals("") ) || (yearTab.Equals("all-year"))? "active in show" : "")"
        role="tabpanel">

          @* @{
            listItems(newsContent);
          } *@

          @{
            listItems(newsContent.Skip((page - 1) * pageSize).Take(pageSize).ToList());
          }
          @{
            pagination(page, totalPages, "0", "", "false");
          }

        </div>
        foreach (var year in groupYear)
        {
          var yearContent = newsContent.Where(x => x.Value<int>("year") == year).ToList();
          var items = newsReleasePage.Children().OrderByDescending(x => x.Value("year")).ThenByDescending(x =>
              x.Value<DateTime>("publishedDate")).Where(x => x.Value<int>("year") == year);
              totalPages = (int)Math.Ceiling((double)items.Count() / (double)pageSize);

          <div id="@year-item"
              class="news-item tab-pane fade year-content @((yearTab.Equals(year.ToString()))? "active in show" : "")"
              role="tabpanel">
            @* @{
              listItems(yearContent);
            } *@

            @{
              listItems(items.Skip((page - 1) * pageSize).Take(pageSize).ToList());
            }
            @{
              pagination(page, totalPages, year.ToString(), searchString, "false");
            }
          </div>
        }
      }
      else
      {
        @if (searchResults != null && searchResults.Any())
        {
          
          totalPages = (int)Math.Ceiling((double)searchResults.Count() / (double)pageSize);
          if (yearTab.Equals("results") == false)
          {
            page = 1;
          }
          else
          {
            Int32.TryParse(HttpContextAccessor.HttpContext.Request.Query["page"], out page);
          }
          

          <div class=" search-results-content tab-pane fade @(yearTab.Equals("results")==true ? "active show" : "")"
            id="search-results" role="tabpanel" aria-labelledby="search-results-content">
            @* @{
              listItems(searchResults.ToList());
            } *@

            @{
              listItems(searchResults.Skip((page - 1) * pageSize).Take(pageSize).ToList());
            }
            @{
              pagination(page, totalPages, "results", searchString, "true");
            }
          </div>

        }
        else
        {
          <div class="search-result-report">
            <div class="rp-data">
              @Umbraco.GetDictionaryValue("Total", "Total"): 0
              @Umbraco.GetDictionaryValue("releases", "release")
            </div>
          </div>
        }
      }

    </div>
  </div>
</div>
@functions {
  private void pagination(int page, int totalPages, string yearTab, string keyword, string searchAction)
  {
    <div class="pagination fy">
      @if (searchAction.Equals("false") == true)
      {
        if (page > 1)
        {
          var href = string.Format("?yearTab={0}&page={1}&searchAction={2}", yearTab, (page - 1), "false");
          <a href="@href" class="prev" aria-label="@Umbraco.GetDictionaryValue("Previous", "Prev")">Prev</a>
        }
        if (totalPages <= 9)
        {
          for (int p = 1; p < totalPages + 1; p++)
          {
            var href = string.Format("?yearTab={0}&page={1}&searchAction={2}", yearTab, @p, "false");
            <a class="@(p == page ? "active" : string.Empty)" href="@href">@p</a>
          }
        }
        else if (totalPages > 9 && page > 5)
        {
          for (int p = page - 4; p <= page + 4; p++)
          {
            var href = string.Format("?yearTab={0}&page={1}&searchAction={2}", yearTab, @p, "false");
            <a class="@(p == page ? "active" : string.Empty)" href="@href">@p</a>
          }
        }
        else
        {
          for (int p = 1; p <= 9; p++)
          {
            var href = string.Format("?yearTab={0}&page={1}&searchAction={2}", yearTab, @p, "false");
            <a class="@(p == page ? "active" : string.Empty)" href="@href">@p</a>
          }
        }

        if (page < totalPages)
        {
          var href = string.Format("?yearTab={0}&page={1}", yearTab, (page + 1));
          <a href="@href" class="next" aria-label="@Umbraco.GetDictionaryValue("Next", "next")">>></a>
        }
      }
      else
      {
        @* if (page > 1)
        {
          var href = string.Format("?yearTab={0}&keyword={1}&page={2}&searchAction={3}",
          yearTab, keyword, (page - 1), "true");
          <a href="@href" class="prev" aria-label="@Umbraco.GetDictionaryValue("Previous", "Prev")">"Prev"</a>
        } *@
        if (totalPages <= 9 || page <= 5)
        {
          if (totalPages > 9)
          {
            totalPages = 9;
          }
          for (int p = 1; p < totalPages + 1; p++)
          {
            var href = string.Format("?yearTab={0}&keyword={1}&page={2}&searchAction={3}",
            yearTab, keyword, @p, "true");
            <a class="@(p == page ? "active" : string.Empty)" href="@href">@p</a>
          }
        }
        else if (totalPages > 9 && page > 5)
        {
          for (int p = page - 4; p <= page + 4; p++)
          {
            var href = string.Format("?yearTab={0}&keyword={1}&page={2}&searchAction={3}",
            yearTab, keyword, @p, "true");
            <a class="@(p == page ? "active" : string.Empty)" href="@href">@p</a>
          }
        }
        else
        {
          for (int p = 1; p <= 9; p++)
          {
            var href = string.Format("?yearTab={0}&keyword={1}&page={2}&searchAction={3}",
            yearTab, keyword, @p, "true");
            <a class="@(p == page ? "active" : string.Empty)" href="@href">@p</a>
          }
        }

        @* if (page < totalPages)
        {
          var href = string.Format("?yearTab={0}&keyword={1}&page={2}&searchAction={3}",
          yearTab, keyword, (page + 1), "true");
          <a href="@href" class="next" aria-label="@Umbraco.GetDictionaryValue("Next", "Next")">>></a>
        } *@
      }
    </div>
  }
}

@functions {
  private void listItems(List<IPublishedContent> yearContent)
  {
    @foreach (var yearItem in yearContent)
    {

      var title = yearItem.Value<String>("title");

      var publishedDate = yearItem.Value<DateTime>("publishedDate") != null ?
      yearItem.Value<DateTime>("publishedDate") : DateTime.Now;
      var date = publishedDate.ToString(@Umbraco.GetDictionaryValue("Date Format - MM/dd/yyyy"));

      <div class="news-item">
        <div class="date">
          <p>@date</p>
        </div>

        <div class="title">
          <a href="@yearItem.Url()" target="_self">
            <p>@title</p>
          </a>
        </div>
      </div>
    }
  }
}
