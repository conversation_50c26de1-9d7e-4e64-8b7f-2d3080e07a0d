@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;

@{
    var eSGTitle = Model.Value("eSGTitle");
    var eSGContent = Model.Value("eSGContent"); 
    var eSGSign = Model.Value("eSGSign");
    var eSGBackground = Model.Value<IPublishedContent>("eSGBackground")?.Url() ?? "";
    var link = (Model.HasValue("linkPage") && Model.Value<Link>("linkPage") != null) ?
    Model.Value<Link>("linkPage").Url : "";
}

<div class="esg-container" style="background-image: url('@eSGBackground')">
    <a class="link-to-esg-page" href="@link" target="_self">
        <div class="container">
            <div class="esg__wrapper">
                <div class="box-content">
                    <h3 class="esg__title title">
                        @eSGTitle
                    </h3>
                    <div class="esg__content text-font-size-24">
                        @eSGContent
                    </div>
                    <div class="esg__sign text-font-size-24">
                        @eSGSign
                    </div>
                </div>
            </div>
        </div>
    </a>
</div>