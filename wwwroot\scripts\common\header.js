$(document).ready(function() {
  $body = $('body');
  var $lang = $('html')[0].lang;
  var $offsetHeightBody = $body.height();

  function searchClick() {

    $('.search_box_btn img').on('click', function(){
      var $this = $(this).closest('.search-wrap');
      $this.toggleClass('search-on');
    });
  }

  function languageDropdown() {
    if($(window).width() <= 1025) {
      var $lang = $('.language');
      var $langList = $('.lang-list');

      $('.wrap-lang > a').on('click', function(e) {
        e.preventDefault();
        $lang.toggleClass('open-search');
        $langList.slideToggle();
      })
    }
  }

  function menuDropdown() {

    if($('.header').hasClass('menu-mobile-dropdown')) {
      var Accordion = function(el, multiple) {
      this.el = el || {};
      this.multiple = multiple || false;
      
      // Variables privadas
      var links = this.el.find('.open-sub');
      // Evento
      links.on('click', {el: this.el, multiple: this.multiple}, this.dropdown)
      }
  
      Accordion.prototype.dropdown = function(e) {
        var $el = e.data.el;
          $this = $(this),
          $next = $this.next();
    
        $next.slideToggle();
        $this.parent().toggleClass('open');
    
        if (!e.data.multiple) {
          $el.find('.sub-menu').not($next).slideUp().parent().removeClass('open');
        };
      }	
    
      var accordion = new Accordion($('.menu-responsive'), false);

      if($(window).width() <= 1200) {
        $('.btn-menu-offcanvas').on('click', function() {
          $this = $(this);
          $this.toggleClass('active-menu');
          $('body').toggleClass('open-menu');
          $this.closest('.header').find('.menu-mobile > .menu-responsive').slideToggle();
        });
        
      }
    }
  }

  function menuOffcanvas() {
    if($('.header').hasClass('menu-mobile-off-canvas')) {
      var Accordion = function(el, multiple) {
        this.el = el || {};
        this.multiple = multiple || false;
        
        // Variables privadas
        var links = this.el.find('.open-sub');
        // Evento
        links.on('click', {el: this.el, multiple: this.multiple}, this.dropdown)
      }
    
      Accordion.prototype.dropdown = function(e) {
        var $el = e.data.el;
          $this = $(this),
          $next = $this.next();
    
        $this.parent().toggleClass('open');
    
        if (!e.data.multiple) {
          $el.find('.sub-menu').not($next).parent().removeClass('open');
        };
      }	
    
      var accordion = new Accordion($('.menu-responsive'), false);
    
      if($(window).width() <= 1200) {
        $('.btn-menu-offcanvas').on('click', function() {
          $this = $(this);
          $this.toggleClass('active-menu');
          $('body').toggleClass('open-menu');
          $this.closest('.site-header').find('.menu-responsive').toggleClass('active');
        });
        $('.back a').on('click', function(){
          $this = $(this);
          $this.closest('.hassub').removeClass('open');
        });
      } 
    }
  }

  function headerSticky() {
    var myElement = document.querySelector(".header");
    var headroom  = new Headroom(myElement);
    headroom.init();
  }

  searchClick();
  languageDropdown();
  menuDropdown();
  menuOffcanvas();
  
  if($offsetHeightBody > 100 && $(window).width() > 1200) {
    headerSticky();
  }
});