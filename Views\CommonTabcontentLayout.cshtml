﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor;
@{
  Layout = "ChildPageIRLayout.cshtml";
  var contents = Model.Children().Where(x => x.IsVisible()).ToList();
  var home = Model.Root();
  var disclaimerLink = "";
  var tab = "";
  if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Query["tab"]))
  {
    tab = HttpContextAccessor.HttpContext.Request.Query["tab"];
  }
}

<div class="tabcontent-container">
  <div class="tab-head">
    <div class="container">
      <ul class="nav" role="tablist">
        @if (contents != null && contents.Any())
        {
          int iIndex = 0;
          foreach (var item in contents)
          {
            var tabName = item.Name.ToLower().Replace(" ", "-").Replace("&", "and").Replace("'", "-");
            var pageUrl = "#";
            var documentType = item.ContentType.Alias;
            var href = "#" + tabName;
            var isTab = true;

            if (documentType.Equals("redirectPage"))
            {
              pageUrl = item.Value<Link>("redirectLink")?.Url;
              href = pageUrl;
              isTab = false;
            }
            var dataToggle = isTab ? "data-toggle=tab" : "";
            if (tab.Equals("") == true)
            {
              <li class="nav-item" role="presentation">
                <a class="nav-link text-font-size-20 @(contents.First() == item ?"active":"")" @dataToggle href="@href"
            aria-expanded="true">@item.Value("pageTitle")
                </a>
              </li>
            }
            else
            {
              <li class="nav-item" role="presentation">
                <a class="nav-link text-font-size-20 @(tabName.Equals(tab)==true ?"active":"")" @dataToggle href="@href"
            aria-expanded="true">@item.Value("pageTitle")
                </a>
              </li>
            }
            iIndex++;
          }
        }
      </ul>
    </div>

  </div>

  <div class="tab-content">
    <div class="container">
      @if (contents != null && contents.Any())
      {
        int iIndex = 0;
        foreach (var item in contents)
        {
          var tabName = item.Name.ToLower().Replace(" ", "-").Replace("&", "and").Replace("'", "-");
          var documentType = item.ContentType.Alias;
          var toolSource = "";
          bool activeTab = false;
          if (tabName.Equals(tab) == true)
          {
            activeTab = true;
          }
          if (documentType.Equals("commonEmbedPage"))
          {
            toolSource = item.Value<string>("toolSource");
          }

          var sourceAdd = toolSource.Length > 0 ? "ea-src=" + toolSource : "";
          if (iIndex > 0)
          {
            sourceAdd = toolSource.Length > 0 ? "data-src=" + toolSource : "";
          }
          var strClass = "tab-pane fade";
          if ((tab.Equals("") == true && contents.First() == item) || (tabName.Equals(tab) == true))
          {
            strClass = strClass + " active in show";
          }
          @* <div id="@tabName" class="tab-pane fade @(contents.First() == item?"active in show":"")" role="tabpanel"> *@
          <div id="@tabName" class="@strClass" role="tabpanel">
            @if (documentType.Equals("commonEmbedPage"))
            {
              <iframe class="eurolandtool" allowtransparency="" frameborder="0" id="<EMAIL>"
          title="@item.Value("pageTitle")" marginheight="0" marginwidth="0" scrolling="no" @sourceAdd width="100%">
              </iframe>
            }
            @if (documentType.Equals("annualStockholdersMeetingsPage"))
            {
              @Html.Partial("StockholdersMeeting/AnnualStockholdersMeetings")
              ;
            }
            @if (documentType.Equals("companyProfilePage"))
            {
              Html.RenderPartial("~/Views/Partials/Corporate Information/Company Profile.cshtml");
            }
            @if (documentType.Equals("corporateGovernancePage"))
            {
              Html.RenderPartial("~/Views/Partials/Corporate Information/Corporate Governance.cshtml");
            }
            @if (documentType.Equals("managementPage"))
            {
              Html.RenderPartial("~/Views/Partials/Corporate Information/Management.cshtml");
            }
            @if (documentType.Equals("boardOfDirectorsPage"))
            {
              Html.RenderPartial("~/Views/Partials/Corporate Information/Board of Directors.cshtml");
            }
            @if (documentType.Equals("newsReleasePage"))
            {
              Html.RenderPartial("~/Views/Partials/News & Events/News Release V2.cshtml");
            }
            @if (documentType.Equals("webcastPresentation"))
            {
              Html.RenderPartial("~/Views/Partials/News & Events/WebcastandPresentation.cshtml");
            }
            @if (documentType.Equals("shareholderMeetingPage"))
            {
              Html.RenderPartial("~/Views/Partials/News & Events/shareholderMeeting.cshtml");
            }
            @if (documentType.Equals("quarterlyResultsPage"))
            {
              Html.RenderPartial("~/Views/QuarterlyResultsLayout.cshtml");
            }
            @if (documentType.Equals("invesrtorRelationContactPage"))
            {
              Html.RenderPartial("~/Views/IRContactLayout.cshtml");
            }
            @if (documentType.Equals("informationRequestPage"))
            {
              Html.RenderPartial("~/Views/InformationRequestLayout.cshtml");
            }
            @if (documentType.Equals("annualAndInterimReportsPage"))
            {
              Html.RenderPartial("~/Views/AnnualAndInterimReports.cshtml");
            }
            @if (documentType.Equals("investorFAQsPage"))
            {
              @* Html.RenderPartial("~/Views/FAQsLayout.cshtml"); *@
              Html.RenderPartial("~/Views/FAQsV2Layout.cshtml");
            }
            @if (documentType.Equals("analystCoveragePage"))
            {
              Html.RenderPartial("~/Views/AnalystCoverageLayout.cshtml");
            }
          </div>
          iIndex++;
        }
      }
    </div>
  </div>
</div>
