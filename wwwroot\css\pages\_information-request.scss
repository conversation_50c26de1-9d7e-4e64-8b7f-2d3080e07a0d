.information-request-container {
    .request {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 2rem;
        @media (min-width: 767px) {
            margin-bottom: 4rem;
        }

        p:first-child {
            font-style: normal;
            font-weight: 400;
            font-size: 1.8rem;
            line-height: 1.5;
            color: $colorBlack;
            @media (min-width: 767px) {
                font-size: 2.4rem;
            }
        }

        p:nth-child(2) {
            font-style: normal;
            font-weight: 400;
            font-size: 1.4rem;
            line-height: 1.5;
            color: $colorGrey;
        }

        @media screen and (max-width: 991px) {
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
        }
    }

    .information-request-form {
        #contact-page-form {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: flex-start;
            margin-left: -4rem;

            .first-name,
            .last-name,
            .email,
            .enquiry-content {
                label {
                    position: relative;

                    &::after {
                        content: "*";
                        position: absolute;
                        color: $colorPink;
                        right: -0.8rem;
                        top: 0;
                    }
                }
            }

            .first-name,
            .last-name,
            .email,
            .business-title,
            .company,
            .country,
            .work-phone,
            .fax,
            .investor-type,
            .enquiry-content {
                width: calc((100% / 3) - 4rem);
                margin-left: 4rem;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: center;
                margin-bottom: 2rem;
                @media (min-width: 767px) {
                    margin-bottom: 4rem;
                }

                label {
                    font-style: normal;
                    font-weight: 400;
                    font-size: 1.4rem;
                    line-height: 1.5;
                    color: $colorBlack;
                    margin-bottom: 1rem;
                    @media (min-width: 767px) {
                        font-size: 1.6rem;
                    }
                }

                textarea,
                input {
                    width: 100%;
                    border: 0.1rem solid $colorGrey;
                    border-radius: 0.4rem;
                    min-height: 4rem;
                    max-height: 4rem;
                    font-style: normal;
                    font-weight: 400;
                    font-size: 1.6rem;
                    line-height: 1.5;
                    color: $colorBlack;
                    padding: 1rem 2rem;

                    &:focus-visible {
                        outline: 0.1rem solid $colorBlue;
                        border: 0.1rem solid $colorBlue;
                    }
                }

                textarea {
                    min-height: 12rem;

                    &::-webkit-scrollbar {
                        width: 1rem;
                        padding-right: 0.5rem;
                    }

                    &::-webkit-scrollbar-track {
                        box-shadow: inset 0 0 0.5rem $colorGrey;
                        border-radius: 2rem;
                    }

                    &::-webkit-scrollbar-thumb {
                        background: $colorPink;
                        border-radius: 1rem;
                    }

                    &::-webkit-scrollbar-thumb:hover {
                        background: $colorBlue;
                    }
                }
            }

            .enquiry-content {
                width: 100%;
            }

            .wrap-buttons {
                margin: 0 auto;
                display: flex;
                align-items: center;
                justify-content: center;

                .submit {
                    outline: none;
                    border: 0;
                }

                .reset {
                    margin-left: 6rem;
                    display: flex;
                    align-items: center;
                    justify-self: center;
                    font-style: normal;
                    font-weight: 400;
                    font-size: 1.6rem;
                    line-height: 1.5;
                    color: $colorGrey;

                    img {
                        margin-right: 1rem;
                    }

                    &:hover {
                        color: $colorBlue;

                        img {
                            animation: rotating 2s linear infinite;
                        }
                    }

                }
            }

            @media screen and (max-width: 991px) {

                .first-name,
                .last-name,
                .email,
                .business-title,
                .company,
                .country,
                .work-phone,
                .fax,
                .investor-type,
                .enquiry-content {
                    width: 100%;

                    textarea {
                        min-height: 18rem;
                    }
                }
            }
        }
    }
}

@-webkit-keyframes rotating {
    from {
        -webkit-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes rotating {
    from {
        -ms-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -ms-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -webkit-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}