footer.site-footer {
  position: relative;
  background-color: $colorBlue;
  padding: 1rem 0;
  z-index: 10;

  .footer-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media (max-width: 1025px) {
      flex-direction: column;
    }
  }

  .developed-by {
    a {
      font-size: 1.4rem;
      line-height: 1.4;
      @media (min-width: 300px) and (max-width: 600px) {
        font-size: 1.2rem;
      }
    }
  }

  a {
    display: block;
    font-size: 1.4rem;
    line-height: 1.5;
    color: #fff;
    @include transition;
    @media (min-width: 300px) and (max-width: 600px) {
      font-size: 1.2rem;
    }

    &:hover {
      animation: textDance 200ms ease-in-out;
    }
  }

  .quick-link {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;

    .ql-item {
      padding-right: 4rem;

      @media (max-width: 1025px) {
        padding: 0;
      }
    }

    @media (max-width: 1025px) {
      justify-content: space-between;
    }
  }

  .col-left {
    display: flex;

    .qr-btn-mobile {
      display: none;

      @media (max-width: 1025px) {
        display: block;
        margin-left: 1rem;

        img {
          width: 25px;
          height: 25px;
        }
      }
    }

    .qr-wrap-footer {
      position: relative;

      .qr-btn {
        width: 25px;
        height: 25px;
      }

      .qr-image {
        position: absolute;
        bottom: 4rem;
        left: 50%;
        transform: translateX(-50%);
        opacity: 0;
        visibility: hidden;
  
        @include transition();
      }
  
      &.open-qr {
        .qr-image {
          opacity: 1;
          visibility: visible;
        }
      }

      @media (max-width: 1025px) {
        display: none;
      }
    }

    @media (max-width: 1025px) {
      margin-bottom: 1rem;
      justify-content: center;
    }
  }

  @media (max-width: 1025px) {
    .col-left,
    .col-right {
      width: 80%;
    }

    .col-right {
      text-align: center;
    }
  }

  @media (max-width: 600px) {
    .col-left,
    .col-right {
      width: 100%;
    }
  }
}