.news-release-section {
    .hide {
        display: none;
    }

    .news-contents {
        .year-content {
            display: none;
            &.show {
                display: block !important;
            }
        }
    }

    .news-release-wrapper {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 5rem;
        margin-top: 5rem;

        .search {
            width: 50%;

            form {
                display: flex;

                .search-box {
                    max-width: 44rem;
                    width: 100%;
                    height: 4rem;
                    border: 0.1rem solid rgba(13, 28, 33, 0.26);
                    border-radius: 3rem;
                    outline: 0;
                    padding-left: 2rem;
                    padding-right: 12rem;
                    font-style: normal;
                    font-weight: 400;
                    font-size: 1.6rem;
                    line-height: 1.4;
                    color: rgba(13, 28, 33, 0.54);

                    @media (max-width: 992px) {
                        height: 4rem;
                    }
                }

                .submit-btn {
                    width: 12rem;
                    height: 4rem;
                    background: #ff558c;
                    border-radius: 2rem;
                    border: 0;
                    outline: 0;
                    font-style: normal;
                    font-weight: 400;
                    font-size: 1.6rem;
                    line-height: 2.4rem;
                    text-align: center;
                    color: #ffffff;
                    margin-left: -12rem;
                    // transform: translateX(-12rem);
                    z-index: 1;

                    @media (max-width: 992px) {
                        height: 4rem;
                        width: 12rem;
                        margin-left: -10rem;
                        // transform: translate(-10rem, 0);
                    }
                }
            }

            @media (max-width: 600px) {
                width: 100%;
            }
        }

        .year-select {
            width: 50%;
            margin-top: 2rem;
            @media (min-width: 767px) {
                margin-top: 0;
            }
            @media (max-width: 600px) {
                width: 100%;
            }

            .year-list {
                display: flex;
                flex-wrap: wrap;
                justify-content: flex-end;
                align-items: flex-end;

                @media (max-width: 600px) {
                    justify-content: flex-start;
                    align-items: flex-start;
                }
            }

            li {
                list-style: none;
                font-style: normal;
                font-weight: 400;
                font-size: 1.4rem;
                line-height: 2.4rem;
                text-align: center;
                color: #0cb6f2;
                margin-left: 1.5rem;
                // font-family: "Sofia Pro";
                font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";

                cursor: pointer;
                @media (min-width: 768px) {
                    font-size: 1.6rem;
                }

                &.active {
                    border-bottom: 0.1rem solid #0cb6f2;
                }
            }
            /*.tab-head {
        border-bottom: 0;

        .nav {
          gap: 0;
          justify-content: flex-start;

          .nav-link {
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 2.4rem;
            text-align: center;
            color: #0cb6f2;
            padding: 0 1rem;

            &.active {
              border-bottom: 0.1rem solid #0cb6f2;
            }
          }

          .search-result-report {
            font-weight: 600;
            width: 100%;
            margin-top: 1.5rem;
            padding-left: 1rem;
          }
        }
      }*/
        }
    }

    .news-header {
        min-height: 6rem;
        // height: 100%;
        background: #0cb6f2;
        display: flex;
        flex-wrap: wrap;

        .date-title,
        .summary-title {
            width: 20%;
            display: flex;
            align-items: center;

            p {
                font-style: normal;
                font-weight: 400;
                font-size: 1.4rem;
                line-height: 1.4;
                color: #ffffff;
                // font-family: "Sofia Pro";
                font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";

                @media (min-width: 768px) {
                    font-size: 1.6rem;
                }
            }
        }

        .date-title {
            width: 20%;
            padding-left: 8rem;

            @media (min-width: 768px) and (max-width: 1200px) {
                padding-left: 3rem;
            }

            @media (min-width: 320px) and (max-width: 767px) {
                padding-left: 1.5rem;
                width: 30%;
            }
        }

        .summary-title {
            width: 80%;

            @media (min-width: 768px) and (max-width: 1200px) {
                padding-left: 1.5rem;
            }

            @media (min-width: 320px) and (max-width: 767px) {
                padding-left: 1.5rem;
                width: 70%;
            }
        }
    }

    .pagination {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        padding-top: 4rem;
        width: 100%;
        background: #fff;

        a {
            font-style: normal;
            font-weight: 400;
            font-size: 1.4rem;
            line-height: 2.4rem;
            text-align: center;
            color: rgba(13, 28, 33, 0.54);
            padding: 0.5rem 1.5rem;
            border: 1px solid transparent;
            // font-family: "Sofia Pro";
            font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";

            @media (min-width: 768px) {
                font-size: 1.6rem;
            }

            &:hover {
                color: #0cb6f2;
            }

            &.active {
                color: #0cb6f2;
                border: 1px solid #0cb6f2;
            }
        }

        .next,
        .prev {
            &:hover {
                color: #0cb6f2;
            }
        }
    }

    .news-item {
        display: flex;
        flex-wrap: wrap;
        background: rgba(13, 28, 33, 0.04);
        min-height: 6rem;
        width: 100%;

        &:nth-child(odd) {
            background: #ffffff;
        }

        .date,
        .title {
            display: flex;
            align-items: center;

            p {
                font-style: normal;
                font-weight: 400;
                font-size: 1.6rem;
                line-height: 1.4;
                padding: 0.5rem 0;
                // font-family: "Sofia Pro";
                font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";

                @media (max-width: 767px) {
                    padding: 1.5rem 0;
                    font-size: 1.4rem;
                }
            }
        }

        .date {
            width: 20%;
            padding-left: 8rem;

            @media (min-width: 768px) and (max-width: 1200px) {
                padding-left: 3rem;
            }

            @media (min-width: 320px) and (max-width: 767px) {
                padding-left: 1.5rem;
                width: 30%;
            }

            p {
                color: #0cb6f2;
            }
        }

        .title {
            width: 80%;

            @media (min-width: 768px) and (max-width: 1200px) {
                padding-left: 1.5rem;
            }

            @media (min-width: 320px) and (max-width: 767px) {
                padding-left: 1.5rem;
                width: 70%;
            }

            a {
                cursor: pointer;
                color: #0d1c21;
                // font-family: "Sofia Pro";
                font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";

                @media (min-width: 200px) and (max-width: 767px) {
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    font-size: 1.4rem;
                }
            }
        }
    }
}
