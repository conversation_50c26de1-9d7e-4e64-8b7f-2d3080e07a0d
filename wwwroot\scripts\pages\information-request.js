function getDoc(frame) {
  var doc = null;

  // IE8 cascading access check
  try {
    if (frame.contentWindow) {
      doc = frame.contentWindow.document;
    }
  } catch (err) {}

  if (doc) {
    // successful getting content
    return doc;
  }

  try {
    // simply checking may throw in ie8 under ssl or mismatched protocol
    doc = frame.contentDocument ? frame.contentDocument : frame.document;
  } catch (err) {
    // last attempt
    doc = frame.document;
  }
  return doc;
}
//above contact page
$("#contact-page-form").submit(function (e) {
  //var $captcha = $(this).find('#contactcaptcha'),
  //response = grecaptcha.getResponse(0);
  //response = grecaptcha.getResponse();
  if (false) {
    $("#contact-page-form .message").removeClass("success");
    $("#contact-page-form .message").addClass("error");
    $("#contact-page-form #cresponse").text("reCAPTCHA is mandatory");
    $(".contacts-msg-error").text("reCAPTCHA is mandatory");
    event.preventDefault();
    // if (!$captcha.hasClass("error")) {
    //     $captcha.addClass("error");
    // }
  } else {
    var formObj = $(this);
    var formURL = formObj.attr("action");
    if (window.FormData !== undefined) {
      // for HTML5 browsers
      var formData = new FormData(this);
      $.ajax({
        url: "/en/contact-form/",
        type: "POST",
        data: formData,
        mimeType: "multipart/form-data",
        contentType: false,
        cache: false,
        processData: false,
        success: function (results, data, textStatus, jqXHR) {
          $("#contact-page-form .message").removeClass("error");
          $("#contact-page-form .message").addClass("success");
          $("#contact-page-form").find("#cresponse").html(results);
          //alert("submission received");
          $(".LoaderForms").css("display", "none");
        },
        error: function (jqXHR, textStatus, errorThrown) {
          $(".LoaderForms").css("display", "none");
        },
      });
      e.preventDefault();
    } else {
      //for olden browsers
      //generate a random id
      var iframeId = "unique" + new Date().getTime();
      //create an empty iframe
      var iframe = $(
        '<iframe src="javascript:false;" name="' + iframeId + '" />'
      );
      //hide it
      iframe.hide();
      //set form target to iframe
      formObj.attr("target", iframeId);
      //Add iframe to body
      iframe.appendTo("body");
      iframe.load(function (e) {
        var doc = getDoc(iframe[0]);
        var docRoot = doc.body ? doc.body : doc.documentElement;
        var data = docRoot.innerHTML;
        //data is returned from server.
      });
    }
    $(".contacts-msg-error").text("");
    //$captcha.removeClass("error");
  }
});
$(".reset").on("click", function (e) {
  e.preventDefault();
  $(".field-reset").val("");
});
/*
///$("#contact-page-form").submit();// JavaScript Document
function onSubmit(token) {
  document.getElementById("contact-page-form").submit();
}
function validate(event) {
  event.preventDefault();
  //response = grecaptcha.getResponse();
  if (!document.getElementById('name').value) {
    alert("You must add text to the required field");
  } else {
    grecaptcha.execute();
    //response = grecaptcha.getResponse();
    //document.getElementById("contact-page-form").submit();
  }
}

function onload() {
  var element = document.getElementById('submit');
  element.onclick = validate;
}

*/
