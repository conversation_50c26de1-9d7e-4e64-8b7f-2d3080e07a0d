﻿.annual-interim-reports-container {
    .tab-head {
        border-bottom: unset;

        .nav {
            gap: 4rem;
            justify-content: center;

            .nav-link {
                width: 12rem;
                border: 1px solid $colorPink;
                border-radius: 2rem;
                color: $colorPink;
                font-weight: 400;
                font-size: 1.6rem;
                line-height: 150%;
                text-align: center;
                padding: 0.7rem 1.5rem;
                // font-family: "Sofia Pro";
                font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
            }

            .nav-link.active {
                border-bottom: unset;
                background-color: $colorPink;
                color: #ffffff;

                &:before {
                    display: none;
                }
            }
        }
    }

    .child-head {
        .nav {
            @media (max-width: 1025px) {
                display: flex;
            }
        }
    }

    .tab-content {
        margin-top: 6rem;
        margin-bottom: 0;

        .nasdaq {
            display: flex;
            gap: 4rem;
            flex-wrap: wrap;

            a {
                display: block;
                width: calc(50% - 2rem);

                @media (max-width: 991px) {
                    width: 100%;
                }
            }

            .item {
                height: 9.5rem;
                background-color: rgba(13, 28, 33, 0.04);
                display: flex;
                padding: 1.5rem 5.2rem 1.7rem 5.2rem;
                align-items: center;
                cursor: pointer;
                -webkit-transform: perspective(1px) translateZ(0);
                transform: perspective(1px) translateZ(0);

                @media (max-width: 550px) {
                    height: auto;
                }

                /* Underline From Center */
                &::before {
                    content: "";
                    position: absolute;
                    z-index: -1;
                    left: 51%;
                    right: 51%;
                    bottom: 0;
                    background: $colorBlue;
                    height: 2px;
                    -webkit-transition-property: left, right;
                    transition-property: left, right;
                    -webkit-transition-duration: 0.4s;
                    transition-duration: 0.4s;
                    -webkit-transition-timing-function: ease-out;
                    transition-timing-function: ease-out;
                }

                &:hover::before {
                    left: 0;
                    right: 0;
                }

                .icon {
                    img {
                        width: 4rem;
                        height: 4rem;
                    }
                }

                .title {
                    p {
                        margin-left: 4rem;
                        font-weight: 400;
                        font-size: 1.6rem;
                        line-height: 150%;
                        color: $colorBlack;
                        // font-family: "Sofia Pro";
                        font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
                    }

                    .date {
                        margin-top: 1rem;
                        font-weight: 400;
                        // font-size: 1.4rem;
                        line-height: 1.5;
                        color: rgba(13, 28, 33, 0.54);
                        // font-family: "Sofia Pro";
                        font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
                    }
                }
            }
        }

        .hkex {
            .hkex-year-content {
                display: flex;
                flex-wrap: nowrap;
                border-bottom: 1px solid rgba(13, 28, 33, 0.26);
                padding-top: 6rem;
                padding-bottom: 6rem;
                justify-content: space-between;

                @media (max-width: 767px) {
                    display: block;
                    text-align: center;
                }

                &:first-child {
                    padding-top: 0;
                }

                &:last-child {
                    // border-bottom: unset;
                    // padding-bottom: 0;
                }

                /*@media (max-width: 1530px) {
                    gap: 12rem;
                }*/

                .year {
                    width: 9rem;
                    text-align: center;

                    @media (max-width: 767px) {
                        width: 100%;
                        margin-bottom: 2rem;
                    }

                    span {
                        font-weight: 400;
                        // font-size: 4rem;
                        line-height: 1.5;
                        color: $colorBlue;
                        // font-family: "Sofia Pro";
                        font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
                    }
                }

                .content {
                    width: calc(100% - 25rem);
                    display: flex;
                    justify-content: flex-start;
                    flex-wrap: wrap;
                    @media (max-width: 1530px) and (min-width: 992px) {
                        width: calc(100% - 13rem);
                    }

                    @media (max-width: 991px) and (min-width: 768px) {
                        //display: block;
                        width: calc(100% - 11rem);
                        gap: 2rem;
                        justify-content: center;
                    }

                    @media (max-width: 767px) {
                        display: block;
                        width: 100%;
                    }

                    .report {
                        // display: flex;
                        display: flex;
                        width: 50%;
                        align-items: flex-start;
                        flex-direction: column;

                        @media screen and (min-width: 992px) and (max-width: 1199px) {
                            // gap: 3rem;
                            align-items: center;
                        }

                        @media (max-width: 991px) {
                            margin-bottom: 4rem;
                            width: calc(50% - 1rem);
                            align-items: center;
                        }

                        @media (max-width: 767px) {
                            width: 100%;
                        }

                        &:last-child {
                            @media (max-width: 991px) {
                                margin-bottom: 0;
                            }
                        }

                        .photo {
                            img {
                                width: auto;
                                max-width: 32rem;

                                @media screen and (min-width: 992px) and (max-width: 1199px) {
                                    max-width: 24rem;
                                }
                                @media screen and (max-width: 991px) {
                                    width: 100%;
                                    height: auto;
                                    object-fit: cover;
                                }

                                @media (max-width: 767px) {
                                    max-width: 100%;
                                }
                            }
                        }

                        .title {
                            a {
                                p {
                                    font-weight: 400;
                                    // font-size: 2.4rem;
                                    line-height: 1.5;
                                    color: $colorBlack;
                                    padding-top: 1rem;
                                    text-align: center;
                                    // font-family: "Sofia Pro";
                                    font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
                                }
                            }
                        }
                    }
                }
            }
        }

        .notice {
            font-weight: 400;
            font-size: 1.6rem;
            line-height: 150%;
            color: #0d1c21;
            margin-top: 6rem;

            b,
            strong {
                font-weight: bold;
            }
        }
    }
}

.financial-information {
    .annual-interim-reports-container {
        .nav-link.active::before {
            opacity: 0;
        }
    }

    .tab-content {
        .inner-content {
            padding-bottom: 12rem;
        }

        margin-bottom: 3rem !important;
    }
}
