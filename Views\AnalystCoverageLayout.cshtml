@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
	var analystCoveragePage = Model.DescendantsOfType("analystCoveragePage")?.FirstOrDefault() ?? null;

	var analystTableItem = analystCoveragePage.Value<IEnumerable<IPublishedElement>>("analystTableItem").ToList();
	var analystInfo = analystCoveragePage.Value<String>("analystInfo");
}

<div class="analyst-coverage-container">
	<div class="box-table">
		<table class="table">
			<thead>
				<tr>
					<th scope="col" class="text-font-size-20">@Umbraco.GetDictionaryValue("Firm")</th>
					<th scope="col" class="text-font-size-20">@Umbraco.GetDictionaryValue("Analyst")</th>
					<th scope="col" class="text-font-size-20">@Umbraco.GetDictionaryValue("E-mail Address")</th>
				</tr>
			</thead>
			<tbody>
				@if (analystTableItem != null && analystTableItem.Any())
				{
					foreach (var item in analystTableItem)
					{
						var firmItem = item.Value("firmItem").ToString();
						var analystItem = item.Value("analystItem").ToString();
						var emailAddress = item.Value("emailAddress").ToString();

						<tr>
							<td>
								@firmItem
							</td>
							<td>
								@analystItem
							</td>
							<td>
								<a href="mailto: @emailAddress">@emailAddress</a>
							</td>
						</tr>
					}
				}
			</tbody>
		</table>
	</div>

	<div class="analyst-info">@Html.Raw(analystInfo)</div>
</div>
