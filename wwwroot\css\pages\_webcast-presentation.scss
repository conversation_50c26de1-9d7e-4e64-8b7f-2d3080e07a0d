.webcast-section {
  .search {
    margin-bottom: 5rem;
    margin-top: 5rem;
    max-width: 60rem;

    form {
      display: flex;

      .search-box {
        max-width: 44rem;
        width: 100%;
        height: 4rem;
        border: 0.1rem solid rgba(13, 28, 33, 0.26);
        border-radius: 3rem;
        outline: 0;
        padding-left: 2rem;
        padding-right: 12rem;
        font-style: normal;
        font-weight: 400;
        font-size: 1.4rem;
        line-height: 1.4;
        color: rgba(13, 28, 33, 0.54);

        @media (max-width: 992px) {
          height: 4rem;
          font-size: 1.6rem;
        }
      }

      .submit-btn {
        width: 12rem;
        height: 4rem;
        background: #ff558c;
        border-radius: 2rem;
        border: 0;
        outline: 0;
        font-style: normal;
        font-weight: 400;
        font-size: 1.4rem;
        line-height: 2.4rem;
        text-align: center;
        color: #ffffff;
        margin-left: -12rem;
        // transform: translateX(-12rem);
        z-index: 1;

        @media (max-width: 992px) {
          height: 4rem;
          width: 12rem;
          margin-left: -10rem;
          font-size: 1.6rem;
          // transform: translate(-10rem, 0);
        }
      }
    }
  }

  .year-select {
    width: calc(100% - 60rem);
  }

  .news-header {
    min-height: 6rem;
    height: 100%;
    background: #0cb6f2;
    display: flex;
    flex-wrap: wrap;

    .date-title,
    .summary-title,
    .webcast-title,
    .presentation-title {
      display: flex;
      align-items: center;

      p {
        font-style: normal;
        font-weight: 400;
        //font-size: 1.6rem;
        line-height: 1.4;
        color: #ffffff;

        @media (min-width: 320px) and (max-width: 420px) {
          font-size: 1.4rem;
        }
      }
    }

    .date-title {
      width: 20%;
      padding-left: 8rem;

      @media (min-width: 768px) and (max-width: 1200px) {
        padding-left: 3rem;
      }

      @media (min-width: 320px) and (max-width: 767px) {
        // padding-left: 1.5rem;
        // width: 20%;
        padding-left: 1.1rem;
        width: 17%;
      }
    }

    .summary-title {
      width: 40%;

      @media (min-width: 768px) and (max-width: 1200px) {
        padding-left: 1.5rem;
      }

      @media (min-width: 320px) and (max-width: 767px) {
        // padding-left: 1.5rem;
        //width: 40%;
        width: 38%;
        padding-left: 2.3rem;
      }
    }

    .webcast-title,
    .presentation-title {
      width: 20%;
      justify-content: center;
      @media (min-width: 320px) and (max-width: 767px) {
        // padding-left: 1.5rem;
        //width: 40%;
        width: 22%;
      }
    }
  }

  .news-item {
    display: flex;
    flex-wrap: wrap;
    background: rgba(13, 28, 33, 0.04);
    min-height: 60px;

    &:nth-child(odd) {
      background: #ffffff;
    }

    .date,
    .title {
      display: flex;
      align-items: center;

      p {
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 1.4;

        @media (min-width: 320px) and (max-width: 420px) {
          font-size: 1.4rem;
        }
      }
    }

    .date {
      width: 20%;
      padding-left: 8rem;

      @media (min-width: 768px) and (max-width: 1200px) {
        padding-left: 3rem;
      }

      @media (min-width: 320px) and (max-width: 767px) {
        //padding-left: 1.5rem;
        padding-left: 0;
        width: 20%;
      }

      a {
        font-size: 1.4rem;
      }

      a,
      p {
        color: #0cb6f2;
        // font-family: "Sofia Pro";
        font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
      }
    }

    .date-v1 {
      @media (min-width: 320px) and (max-width: 520px) {
        display: none;
      }
    }

    .date-v2 {
      display: none;

      @media (min-width: 320px) and (max-width: 520px) {
        display: block;
      }
    }

    .title {
      width: 40%;

      @media (min-width: 768px) and (max-width: 1200px) {
        padding-left: 1.5rem;
      }

      @media (min-width: 320px) and (max-width: 767px) {
        padding-left: 1rem;
        width: 40%;
      }

      p {
        color: #0d1c21;
        padding: 1.5rem 0.5rem;

        @media (max-width: 767px) {
          padding: 1.5rem 0.5rem;
        }
      }
    }

    .webcast,
    .presentation {
      width: 20%;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: center;

      img {
        width: 32px;
        height: auto;
        object-fit: cover;

        @media (min-width: 320px) and (max-width: 420px) {
          width: 26px;
        }
      }
    }
  }
}
