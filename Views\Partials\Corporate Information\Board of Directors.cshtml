﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;
@{
    var boardOfDirectorsPage = Model.DescendantsOfType("boardOfDirectorsPage")?.FirstOrDefault() ?? null;
    var members = boardOfDirectorsPage.Children.Where(x => x.IsVisible()).OrderBy(x => x.SortOrder).ToList();
    var arrowIcon = "/media/qepdhlie/arrow.png";
}
<div class="board-of-directors-container">
    @foreach (var item in members)
    {
        var personName = item.Value("personName").ToString();
        var position = item.Value("position").ToString();
        var personInformation = item.Value("personInformation").ToString();
        <div class="item">
            <div class="head-info">
                <div class="person-info">
                    <div class="name">@personName</div>
                    <div class="position">@position</div>
                </div>
                <div class="arrow"><img src="@arrowIcon" alt="Arrow icon" /></div>
            </div>
            <div class="desc">@Html.Raw(personInformation)</div>
        </div>
    }
</div>