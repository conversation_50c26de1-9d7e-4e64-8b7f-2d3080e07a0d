// @font-face {
//     font-family: "Sofia Pro";
//     src: 
//         url("../../css/global/SofiaProRegularAz/SofiaProRegularAz_03a726e6.woff2") format("woff2"),
//         url("../../css/global/SofiaProRegularAz/SofiaProRegularAz_03a726e6.woff") format("woff"),
//         url("../../css/global/SofiaProRegularAz/SofiaProRegularAz_03a726e6.eot"),
//         url('../../css/global/SofiaProRegularAz/SofiaProRegularAz_03a726e6.ttf') format('truetype');
// }

// @font-face {
//     font-family: "Sofia Pro Bold";
//     src: url("../../css/global/SofiaProBoldAz/SofiaProBoldAz_e740082a.woff2") format("woff2"),
//         url("../../css/global/SofiaProBoldAz/SofiaProBoldAz_e740082a.woff") format("woff"),
//         url("../../css/global/SofiaProBoldAz/SofiaProBoldAz_e740082a.eot"),
//         url('../../css/global/SofiaProBoldAz/SofiaProBoldAz_e740082a.ttf') format('truetype');
// }

// @font-face {
//     font-family: "Microsoft Yahei Bold";
//     src: url('../global/MicrosoftYaheiBold/Microsoft-YaHei-Bold-Font.ttf') format('truetype');
// }
