﻿@using System.ServiceModel.Syndication

@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
	Layout = null;
}

@{
    try
    {
                //var pageModel = Model.Root().DescendantsOfType("industryResearchReportsPage")?.LastOrDefault() ?? null;
        //var contents = pageModel.Children.Where(x => x.IsVisible())
        //            .OrderByDescending(x => x.Value("createdDate"))
        //            .ThenByDescending(x => x.SortOrder);
        //string _baseurl = Context.Request.Scheme + "://" + Context.Request.Host;// HttpContext.Request.Url.Authority;
        //SyndicationFeed feed = GetFeed(contents, _baseurl);
        ////SyndicationFeed feed = CMSCommon.Controllers.RssSyndicator.GetFeed(contents, _baseurl);        
        //Context.Response.ContentType = "application/rss+xml";
        //var formatter = new Rss20FeedFormatter(feed);
        //using (XmlWriter writer = XmlWriter.Create(Context.Response.Body))
        //{
        //    formatter.WriteTo(writer);
        //}
    }
    catch (Exception ex)
    {
        string sr = ex.ToString();
    }


}
@functions {
   //private static List<SyndicationItem> GetPosts(string pbaseUrl, IEnumerable<IPublishedContent> pPostMetaData)
    //{
        //var items = new List<SyndicationItem>();

        //foreach (var post in pPostMetaData.ToList())
        //{
        //    var item = new SyndicationItem();
        //    string title = post.Value("company").ToString();
        //    item.Title = TextSyndicationContent.CreatePlaintextContent(title);
        //    string researchReport = post.Value("researchReport").ToString();
        //    item.Content = TextSyndicationContent.CreatePlaintextContent(researchReport);

        //    var date = post.Value<DateTime>("createdDate");
        //    item.PublishDate = new DateTimeOffset(date);
        //    //item.Summary = new CDataSyndicationContent(new TextSyndicationContent(content, TextSyndicationContentKind.Html));
        //    items.Add(item);

        //}

        //return items;
    //}
}