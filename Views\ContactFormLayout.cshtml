﻿@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@using System.Net;
@using System.Net.Mail;
@using Newtonsoft.Json;
@using System.Net.Http;
@using System.Net.Http.Headers;
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor;
@using Microsoft.Extensions.Options;
@using Umbraco.Cms.Core.Configuration.Models;
@inject IOptions<GlobalSettings> globalSettings;

@{
    Layout = null;
}
@{

    try
    {
        var receiveEmail = "<EMAIL>";
        var receiveEmails = Model.Value<string[]>("receiveEmails");

        string defaultEmail = "<EMAIL>";
        var sendToEmail = receiveEmail;
        var EmailFrom = defaultEmail;

        string submitMessage = "";
        string firstName = Umbraco.GetDictionaryValue("First Name");
        string lastName = Umbraco.GetDictionaryValue("Last Name");
        string email = Umbraco.GetDictionaryValue("Email");
        string businessTitle = Umbraco.GetDictionaryValue("Business Title");        
        string company = Umbraco.GetDictionaryValue("Company");
        string country = Umbraco.GetDictionaryValue("Country");
        string workPhone = Umbraco.GetDictionaryValue("Work Phone");
        string fax = Umbraco.GetDictionaryValue("fax");
        string investorType = Umbraco.GetDictionaryValue("Investor Type");
        string enquiryContent = Umbraco.GetDictionaryValue("Enquiry Content");

        @* string recaptchaResponse = Request["g-recaptcha-response"] as string; *@
        //g-recaptcha-response

        @* if (!CMSCommon.Controllers.CMSCommon.CaptchaValidate(recaptchaResponse))
        {
            <div class="alert alert-error">@Umbraco.GetDictionaryValue("Error: The captcha is invalid")</div>
            return;
        } *@

        var EmailTitle = "" + Umbraco.GetDictionaryValue("Bilibili Website General Enquiry Message") + "";
        var language = System.Threading.Thread.CurrentThread.CurrentCulture.Name;
        GlobalSettings _globalSettings;
        _globalSettings = globalSettings.Value;
        SmtpClient smtpClient = new SmtpClient(_globalSettings.Smtp.Host, _globalSettings.Smtp.Port);
        smtpClient.Credentials = new System.Net.NetworkCredential(_globalSettings.Smtp.From, _globalSettings.Smtp.Password);

        MailMessage mail = new MailMessage();
        mail.From = new MailAddress(EmailFrom);
       

        mail.IsBodyHtml = true;
        if (language.Equals("ar-SA"))
        {
            mail.BodyEncoding = System.Text.Encoding.GetEncoding("iso-8859-1");
        }
        else
        {
            mail.BodyEncoding = System.Text.UTF8Encoding.UTF8;
        }

        mail.Subject = EmailTitle;

        var HtmlBody = "";

        if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Form["firstName"]))
        {
            mail.Body += "<br />" + firstName + ": " + HttpContextAccessor.HttpContext.Request.Form["firstName"] as string;
            
        }
        if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Form["lastName"]))
        {
            mail.Body += "<br />" + lastName + ": " + HttpContextAccessor.HttpContext.Request.Form["lastName"] as string;
        }
        if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Form["email"]))
        {
            mail.Body += "<br />" + email + ": " + email as string;
        }
        if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Form["businessTitle"]))
        {
            mail.Body += "<br />" + businessTitle + ": " + HttpContextAccessor.HttpContext.Request.Form["businessTitle"] as string;
        }
        if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Form["company"]))
        {
            mail.Body += "<br />" + company + ": " + HttpContextAccessor.HttpContext.Request.Form["company"] as string;
        }
        if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Form["country"]))
        {
            mail.Body += "<br />" + country + ": " + HttpContextAccessor.HttpContext.Request.Form["country"] as string;
        }
        if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Form["workPhone"]))
        {
            mail.Body += "<br />" + workPhone + ": " + HttpContextAccessor.HttpContext.Request.Form["workPhone"] as string;
        }
        if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Form["fax"]))
        {
            mail.Body += "<br />" + fax + ": " + HttpContextAccessor.HttpContext.Request.Form["fax"] as string;
        }
        if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Form["investorType"]))
        {
            mail.Body += "<br />" + investorType + ": " + HttpContextAccessor.HttpContext.Request.Form["investorType"] as string;
        }
        if (!string.IsNullOrEmpty(HttpContextAccessor.HttpContext.Request.Form["enquiryContent"]))
        {
            mail.Body += "<br />" + enquiryContent + ": " + HttpContextAccessor.HttpContext.Request.Form["enquiryContent"] as string;
        }

        List<string> lstEmails = new List<string>();
        if (receiveEmails.Any())
        {
            //add content email to a list
            foreach (var _email in receiveEmails)
            {
                lstEmails.Add(_email);
            }
        }
        else
        {
            //add default email
            lstEmails.Add(receiveEmail);
        }
        foreach (var _email in lstEmails)
        {
            try
            {
                //send email
                //CMSCommon.Controllers.CMSCommon.SendAnEmail(_email, EmailTitle, HtmlBody);
                mail.To.Clear();
                mail.To.Add(new MailAddress(_email, "Admin"));
                smtpClient.Send(mail);

            }
            catch (Exception ex)
            {
                //send email to admin about the system error
                //CMSCommon.Controllers.CMSCommon.SendErrorEmail("Error sending email", ex.ToString());

            }
        }

        submitMessage += Umbraco.GetDictionaryValue("Your email has been sent, [firstName] [lastName]. We'll be in touch with you as soon as possible!");
        submitMessage = submitMessage.Replace("[firstName]", HttpContextAccessor.HttpContext.Request.Form["firstName"]);
        <div class="alert alert-success">Success: @submitMessage</div>
    }

    catch (Exception ex)
    {
        string errorSubmitMessage = ex.Message.ToString();
        <div class="alert alert-error">Error: @errorSubmitMessage</div>
    }

}
