@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;

@{
	var annualReportsPage = Model.DescendantsOfType("annualReportsPage")?.FirstOrDefault() ?? null;
	var contents = annualReportsPage != null ?
		annualReportsPage.Children().Where(x => x.IsVisible()).OrderBy(x => x.SortOrder).ToList() : null;
}

<div class="annual-reports-container">
    <div class="tab-head child-head">
        <ul class="nav" role="tablist">
            @if (contents != null && contents.Any())
            {
                int iIndex = 0;
                foreach (var item in contents)
                {
                    var tabName = item.Name.ToLower().Replace(" ", "-").Replace("&", "and").Replace("'", "-");
                    var href = "#" + tabName;
                    var pageTitleChild = item.Value("pageTitle");

                    <li class="nav-item device_buttons" role="presentation" data-button-count="@iIndex">
                        <a class="nav-link @(contents.First() == item ? "active" : "")" data-toggle="tab" href="@href">
                            @pageTitleChild
                        </a>
                    </li>

                    iIndex++;
                }
            }
        </ul>
    </div>

    <div class="tab-content">
        <div class="container">
        @if (contents != null && contents.Any())
        {
            int iIndex = 0;
            foreach (var item in contents)
            {
                var tabName = item.Name.ToLower().Replace(" ", "-").Replace("&", "and").Replace("'", "-");
                var strClass = "tab-pane fade";
                if (contents.First() == item)
                {
                    strClass = strClass + " active in show";
                }

                <div id="@tabName" class="@strClass content-tab" role="tabpanel" data-content-count="@iIndex">
                    @{
                    generateContent(item);
                    }
                </div>

                iIndex++;
            }
        }
        </div>
    </div>
</div>

@functions {
    private void generateContent(IPublishedContent page)
    {
        // Hiển thị content của trang con
        var pageContent = page.Value<string>("pageContent");
        var notice = page.Value<string>("notice");

        if (!string.IsNullOrEmpty(pageContent))
        {
            <div class="page-content">
                @Html.Raw(pageContent)
            </div>
        }

        // Hiển thị các reports nếu có
        var reports = page.Children().Where(x => x.IsVisible()).ToList();
        if (reports != null && reports.Any())
        {
            <div class="reports-section">
                @foreach (var report in reports)
                {
                    var title = report.Value("title")?.ToString();
                    var fileUrl = "";
                    if (report.HasValue("file") && report.Value<IPublishedContent>("file") != null)
                    {
                        fileUrl = report.Value<IPublishedContent>("file").Url();
                    }
                    var publishedDate = report.HasValue("publishedDate") ?
                        report.Value<DateTime>("publishedDate").ToString("MM/dd/yyyy") : "";

                    if (!string.IsNullOrEmpty(title))
                    {
                        <div class="report-item">
                            @if (!string.IsNullOrEmpty(fileUrl))
                            {
                                <a href="@fileUrl" target="_blank" title="@title">
                                    <div class="item">
                                        <div class="icon"><img src="/media/0xzbdlyq/pdf-icon.png" alt="PDF Icon" /></div>
                                        <div class="title">
                                            @Html.Raw(title)
                                            @if (!string.IsNullOrEmpty(publishedDate))
                                            {
                                                <p class="date text-font-size-14">@publishedDate</p>
                                            }
                                        </div>
                                    </div>
                                </a>
                            }
                            else
                            {
                                <div class="item">
                                    <div class="title">@Html.Raw(title)</div>
                                    @if (!string.IsNullOrEmpty(publishedDate))
                                    {
                                        <p class="date text-font-size-14">@publishedDate</p>
                                    }
                                </div>
                            }
                        </div>
                    }
                }
            </div>
        }

        // Hiển thị notice nếu có
        if (!string.IsNullOrEmpty(notice))
        {
            <div class="notice">
                @Html.Raw(notice)
            </div>
        }
    }
}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to activate tab based on hash
    function activateTabFromHash() {
        var hash = window.location.hash;
        if (hash) {
            console.log('Activating tab for hash:', hash);

            // Find the tab link with matching href
            var tabLink = document.querySelector('.nav-link[href="' + hash + '"]');

            if (tabLink) {
                // Remove active class from all tabs and tab content
                var allTabLinks = document.querySelectorAll('.nav-link');
                var allTabPanes = document.querySelectorAll('.tab-pane');

                allTabLinks.forEach(function(link) {
                    link.classList.remove('active');
                });

                allTabPanes.forEach(function(pane) {
                    pane.classList.remove('active', 'in', 'show');
                });

                // Add active class to the target tab link
                tabLink.classList.add('active');

                // Add active class to the target tab content
                var targetTab = document.querySelector(hash);
                if (targetTab) {
                    targetTab.classList.add('active', 'in', 'show');
                }

                console.log('Tab activated successfully');
            } else {
                console.log('Tab link not found for hash:', hash);
            }
        }
    }

    // Activate tab on page load
    activateTabFromHash();

    // Handle tab clicks to update URL hash
    var tabLinks = document.querySelectorAll('.nav-link[data-toggle="tab"]');
    tabLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            var href = this.getAttribute('href');
            if (href && href.indexOf('#') === 0) {
                // Update URL hash without triggering page reload
                if (history.pushState) {
                    history.pushState(null, null, href);
                } else {
                    window.location.hash = href;
                }
                console.log('Updated URL hash to:', href);
            }
        });
    });

    // Handle browser back/forward buttons
    window.addEventListener('hashchange', function() {
        console.log('Hash changed, activating tab');
        activateTabFromHash();
    });
});
</script>