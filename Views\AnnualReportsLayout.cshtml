@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models;
@using Microsoft.AspNetCore.Http;

@{
    var annualReportsPage = Model.DescendantsOfType("annualReportsPage")?.FirstOrDefault();
    var contents = annualReportsPage != null
        ? annualReportsPage.Children().Where(x => x.IsVisible()).OrderBy(x => x.SortOrder).ToList()
        : null;

    // Lấy tab từ query string (?tab=nasdaq)
    var query = Context.Request.Query["tab"].ToString().ToLower();
}

<div class="annual-reports-container">
    <div class="list-tab">
        @if (contents != null && contents.Any())
        {
            foreach (var item in contents)
            {
                var title = item.Value("pageTitle")?.ToString() ?? "";
                var slug = title.ToLower(); // dùng slug để gắn vào query

                var isActive = query == slug || (string.IsNullOrEmpty(query) && contents.First() == item);

                <a href="?tab=@slug"
                   class="tab-link @(isActive ? "active" : "")"
                   data-tab="<EMAIL>"
                   data-slug="@slug">
                    @title
                </a>
            }
        }
    </div>

    <div class="list-content">
        @if (contents != null && contents.Any())
        {
            foreach (var item in contents)
            {
                var title = item.Value("pageTitle")?.ToString() ?? "";
                var slug = title.ToLower();
                var isActive = query == slug || (string.IsNullOrEmpty(query) && contents.First() == item);

                <div class="content tab-content @(isActive ? "active" : "")" id="<EMAIL>">
                    @if (slug.Contains("hkex"))
                    {
                        @await Html.PartialAsync("HKEXLayout", item)
                    }
                    else if (slug.Contains("nasdaq"))
                    {
                        @await Html.PartialAsync("NASDAQLayout", item)
                    }
                    else
                    {
                        <p>Không có layout phù hợp</p>
                    }
                </div>
            }
        }
    </div>
</div>

<style>
    .tab-content { display: none; }
    .tab-content.active { display: block; }
    .tab-link.active { font-weight: bold; }
</style>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const tabs = document.querySelectorAll(".tab-link");
        const contents = document.querySelectorAll(".tab-content");

        tabs.forEach(tab => {
            tab.addEventListener("click", function (e) {
                e.preventDefault();

                const slug = this.dataset.slug;
                const target = this.dataset.tab;

                // set URL ?tab=slug
                const newUrl = window.location.pathname + "?tab=" + slug;
                history.pushState({}, "", newUrl);

                // reset UI
                tabs.forEach(t => t.classList.remove("active"));
                contents.forEach(c => c.classList.remove("active"));

                this.classList.add("active");
                document.getElementById(target).classList.add("active");
            });
        });
    });
</script>
