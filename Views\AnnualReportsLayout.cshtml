@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;

@{
	var annualReportsPage = Model.DescendantsOfType("annualReportsPage")?.FirstOrDefault() ?? null;
	var contents = annualReportsPage != null ?
		annualReportsPage.Children().Where(x => x.IsVisible()).OrderBy(x => x.SortOrder).ToList() : null;
}

<div class="annual-reports-container">
    <div class="tab-head child-head">
        <ul class="nav" role="tablist">
            @if (contents != null && contents.Any())
            {
                int iIndex = 0;
                foreach (var item in contents)
                {
                    var pageUrl = item.Url();
                    var isCurrentPage = Model.Id == item.Id || Model.AncestorsOrSelf().Any(x => x.Id == item.Id);
                    var pageTitleChild = item.Value("pageTitle");

                    <li class="nav-item device_buttons" role="presentation" data-button-count="@iIndex">
                        <a class="nav-link @(isCurrentPage ? "active" : "")" href="@pageUrl">
                            @pageTitleChild
                        </a>
                    </li>

                    iIndex++;
                }
            }
        </ul>
    </div>

    <div class="tab-content">
        <div class="container">
        @{
            // Tìm trang con hiện tại dựa trên Model
            var currentChildPage = contents?.FirstOrDefault(x => x.Id == Model.Id);

            // Nếu đang ở trang cha, hiển thị trang con đầu tiên
            if (currentChildPage == null && contents != null && contents.Any())
            {
                currentChildPage = contents.First();
            }

            if (currentChildPage != null)
            {
                <div class="content-tab active">
                    @{
                    generateContent(currentChildPage);
                    }
                </div>
            }
        }
        </div>
    </div>
</div>

@functions {
    private void generateContent(IPublishedContent page)
    {
        // Hiển thị content của trang con
        var pageContent = page.Value<string>("pageContent");
        var notice = page.Value<string>("notice");

        if (!string.IsNullOrEmpty(pageContent))
        {
            <div class="page-content">
                @Html.Raw(pageContent)
            </div>
        }

        // Hiển thị các reports nếu có
        var reports = page.Children().Where(x => x.IsVisible()).ToList();
        if (reports != null && reports.Any())
        {
            <div class="reports-section">
                @foreach (var report in reports)
                {
                    var title = report.Value("title")?.ToString();
                    var fileUrl = "";
                    if (report.HasValue("file") && report.Value<IPublishedContent>("file") != null)
                    {
                        fileUrl = report.Value<IPublishedContent>("file").Url();
                    }
                    var publishedDate = report.HasValue("publishedDate") ?
                        report.Value<DateTime>("publishedDate").ToString("MM/dd/yyyy") : "";

                    if (!string.IsNullOrEmpty(title))
                    {
                        <div class="report-item">
                            @if (!string.IsNullOrEmpty(fileUrl))
                            {
                                <a href="@fileUrl" target="_blank" title="@title">
                                    <div class="item">
                                        <div class="icon"><img src="/media/0xzbdlyq/pdf-icon.png" alt="PDF Icon" /></div>
                                        <div class="title">
                                            @Html.Raw(title)
                                            @if (!string.IsNullOrEmpty(publishedDate))
                                            {
                                                <p class="date text-font-size-14">@publishedDate</p>
                                            }
                                        </div>
                                    </div>
                                </a>
                            }
                            else
                            {
                                <div class="item">
                                    <div class="title">@Html.Raw(title)</div>
                                    @if (!string.IsNullOrEmpty(publishedDate))
                                    {
                                        <p class="date text-font-size-14">@publishedDate</p>
                                    }
                                </div>
                            }
                        </div>
                    }
                }
            </div>
        }

        // Hiển thị notice nếu có
        if (!string.IsNullOrEmpty(notice))
        {
            <div class="notice">
                @Html.Raw(notice)
            </div>
        }
    }
}