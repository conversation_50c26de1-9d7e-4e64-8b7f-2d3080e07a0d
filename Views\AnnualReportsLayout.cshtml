@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models;
@using Microsoft.AspNetCore.Http;

@{
    var annualReportsPage = Model.DescendantsOfType("annualReportsPage")?.FirstOrDefault();
    var contents = annualReportsPage != null
        ? annualReportsPage.Children().Where(x => x.IsVisible()).OrderBy(x => x.SortOrder).ToList()
        : null;

    // Lấy tab từ query string (?tab=nasdaq)
    var query = Context.Request.Query["tab"].ToString().ToLower();
}

<div class="annual-reports-container">
    <div class="list-tab">
        @if (contents != null && contents.Any())
        {
            foreach (var item in contents)
            {
                var title = item.Value("pageTitle")?.ToString() ?? "";
                var slug = title.ToLower(); // dùng slug để gắn vào query

                var isActive = query == slug || (string.IsNullOrEmpty(query) && contents.First() == item);

                <a href="#annual-reports?tab=@slug"
                   class="tab-link @(isActive ? "active" : "")"
                   data-tab="<EMAIL>"
                   data-slug="@slug">
                    @title
                </a>
            }
        }
    </div>

    <div class="list-content">
        @if (contents != null && contents.Any())
        {
            foreach (var item in contents)
            {
                var title = item.Value("pageTitle")?.ToString() ?? "";
                var slug = title.ToLower();
                var isActive = query == slug || (string.IsNullOrEmpty(query) && contents.First() == item);

                <div class="content tab-contents @(isActive ? "active" : "")" id="<EMAIL>">
                    @if (slug.Contains("hkex"))
                    {
                        @await Html.PartialAsync("HKEXLayout", item)
                    }
                    else if (slug.Contains("nasdaq"))
                    {
                        @await Html.PartialAsync("NASDAQLayout", item)
                    }
                    else
                    {
                        <p>Không có layout phù hợp</p>
                    }
                </div>
            }
        }
    </div>
</div>

<style>
    .tab-contents { display: none; }
    .tab-contents.active { display: block; }
    .tab-link.active { font-weight: bold; }
</style>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const tabs = document.querySelectorAll(".annual-reports-container .tab-link");
        const contents = document.querySelectorAll(".annual-reports-container .tab-contents");

        tabs.forEach(tab => {
            tab.addEventListener("click", function (e) {
                e.preventDefault();

                const slug = this.dataset.slug;
                const target = this.dataset.tab;

                // Preserve hash and add query parameter
                const currentHash = window.location.hash || '#annual-reports';
                const newUrl = window.location.pathname + "?tab=" + slug + currentHash;
                history.pushState({}, "", newUrl);

                // reset UI
                tabs.forEach(t => t.classList.remove("active"));
                contents.forEach(c => c.classList.remove("active"));

                this.classList.add("active");
                document.getElementById(target).classList.add("active");
            });
        });

        // Handle page load with both query and hash
        function initializeFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const tabParam = urlParams.get('tab');

            if (tabParam) {
                // Find and activate the tab based on query parameter
                const targetTab = document.querySelector(`.annual-reports-container .tab-link[data-slug="${tabParam}"]`);
                const targetContent = document.querySelector(`.annual-reports-container #tab-${targetTab?.dataset.tab.replace('tab-', '')}`);

                if (targetTab && targetContent) {
                    // Reset all tabs
                    tabs.forEach(t => t.classList.remove("active"));
                    contents.forEach(c => c.classList.remove("active"));

                    // Activate target tab
                    targetTab.classList.add("active");
                    targetContent.classList.add("active");
                }
            }
        }

        // Initialize on page load
        initializeFromUrl();

        // Handle browser back/forward
        window.addEventListener('popstate', function() {
            initializeFromUrl();
        });
    });
</script>
