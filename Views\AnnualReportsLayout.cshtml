@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;

@{
	var annualReportsPage = Model.DescendantsOfType("annualReportsPage")?.FirstOrDefault() ?? null;
	var contents = annualReportsPage.Children().Where(x => x.IsVisible()).ToList();
}

<div class="annual-rp-new">
	@if (contents != null && contents.Any())
	{
		foreach (var item in contents)
		{
			var pageTitleChild = @item.Value("pageTitle");

			<div class="title-child">@pageTitleChild</div>

			<a href="@item.Url()">@pageTitleChild</a>
		}
	}
</div>