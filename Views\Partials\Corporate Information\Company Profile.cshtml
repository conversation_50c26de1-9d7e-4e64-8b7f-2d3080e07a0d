﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;
@{
    var companyProfilePage = Model.DescendantsOfType("companyProfilePage")?.FirstOrDefault() ?? null;
    var contentBlocks = companyProfilePage.Value<IEnumerable<IPublishedElement>>("contentBlock").ToList();
}
<div class="company-profile-container">
    @if(contentBlocks.Count()>0){
        foreach (var item in contentBlocks)
        {
            var photoUrl = "";
            if(item.HasValue("photo")&&item.Value<IPublishedContent>("photo")!=null){
                photoUrl = item.Value<IPublishedContent>("photo").Url();
            }
            var content = item.Value("content").ToString();
            <div class="content-block">
                <div class="photo"><img src="@photoUrl" alt=""/></div>
                <div class="content">@Html.Raw(content)</div>
                
            </div>
        }
    }
</div>