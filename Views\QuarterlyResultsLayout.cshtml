﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;

@{
    var quarterlyResultsPage = Model.DescendantsOfType("quarterlyResultsPage")?.FirstOrDefault() ?? null;
    var quarterlyResultsPageContent = quarterlyResultsPage != null ?
    quarterlyResultsPage.Children().OrderByDescending(x => x.Value("year")).ToList() : null;
    var rightImg = "/media/lb0emzan/right-arrow-icon.png";
    var leftImg = "/media/t5qhwc0k/left-arrow-icon.png";
}
<div class="quarterly-results-container">
    @if (quarterlyResultsPageContent.Any())
    {
        <div class="reports-table">
            <div class="left-title">
                <div class="type"></div>
                <div class="type">@Umbraco.GetDictionaryValue("Press Release")</div>
                <div class="type">@Umbraco.GetDictionaryValue("Webcast ")</div>
                <div class="type">@Umbraco.GetDictionaryValue("SEC Filing")</div>
                <div class="type">@Umbraco.GetDictionaryValue("HKEX Filing")</div>
            </div>
            <div class="right-content">
                <div class="quarterly-results-swiper-prev">
                    <img class="left" src="@leftImg" />
                </div>
                <div class="quarterly-results-swiper-next">
                    <img class="right" src="@rightImg" />
                </div>
                <div class="swiper quarterly-results-swiper">
                    <div class="swiper-wrapper">
                        @foreach (var item in quarterlyResultsPageContent)
                        {
                            var year = item.Value("year").ToString();

                            //Q1 Content
                            var q1PressReleasesFile = item.Value<IPublishedContent>("q1PressReleasesFile")?.Url() ??
                            "javascript:void(0);";
                            var q1WebcastsFile = item.Value<Link>("q1WebcastFile")?.Url ??
                            "javascript:void(0);";
                            var q1SECFilingFile = item.Value<IPublishedContent>("q1SECFilingFile")?.Url() ??
                            "javascript:void(0);";
                            var q1HKEXFilingFile = item.Value<IPublishedContent>("q1HKEXFilingFile")?.Url() ??
                            "javascript:void(0);";

                            //Q2 Content
                            var q2PressReleasesFile = item.Value<IPublishedContent>("q2PressReleasesFile")?.Url() ??
                            "javascript:void(0);";
                            var q2WebcastsFile = item.Value<Link>("q2WebcastFile")?.Url ??
                            "javascript:void(0);";
                            var q2SECFilingFile = item.Value<IPublishedContent>("q2SECFilingFile")?.Url() ??
                            "javascript:void(0);";
                            var q2HKEXFilingFile = item.Value<IPublishedContent>("q2HKEXFilingFile")?.Url() ??
                            "javascript:void(0);";

                            //Q3 Content
                            var q3PressReleasesFile = item.Value<IPublishedContent>("q3PressReleasesFile")?.Url() ??
                            "javascript:void(0);";
                            var q3WebcastsFile = item.Value<Link>("q3WebcastFile")?.Url ??
                            "javascript:void(0);";
                            var q3SECFilingFile = item.Value<IPublishedContent>("q3SECFilingFile")?.Url() ??
                            "javascript:void(0);";
                            var q3HKEXFilingFile = item.Value<IPublishedContent>("q3HKEXFilingFile")?.Url() ??
                            "javascript:void(0);";
                            
                            //Q4 Content
                            var q4PressReleasesFile = item.Value<IPublishedContent>("q4PressReleasesFile")?.Url() ??
                            "javascript:void(0);";
                            var q4WebcastsFile = item.Value<Link>("q4WebcastFile")?.Url ??
                            "javascript:void(0);";
                            var q4SECFilingFile = item.Value<IPublishedContent>("q4SECFilingFile")?.Url() ??
                            "javascript:void(0);";
                            var q4HKEXFilingFile = item.Value<IPublishedContent>("q4HKEXFilingFile")?.Url() ??
                            "javascript:void(0);";

                            <div class="swiper-slide item ">
                                <div class="year">@year</div>
                                <div class="quaterly-results-year-content">
                                    @for (int i = 0; i < 4; i++)
                                    {
                                        if (i == 0)
                                        {

                                            //Press Releases
                                            <div class="row-data-content">
                                                <div class="cell @(q1PressReleasesFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q1PressReleasesFile" title="@year"
                                       target="@(q1PressReleasesFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q1")</a>
                                                </div>
                                                <div class="cell @(q2PressReleasesFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q2PressReleasesFile" title="@year"
                                       target="@(q2PressReleasesFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q2")</a>
                                                </div>
                                                <div class="cell @(q3PressReleasesFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q3PressReleasesFile" title="@year"
                                       target="@(q3PressReleasesFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q3")</a>
                                                </div>
                                                <div class="cell @(q4PressReleasesFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q4PressReleasesFile" title="@year"
                                       target="@(q4PressReleasesFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q4")</a>
                                                </div>
                                            </div>
                                        }
                                        if (i == 1)

                                        {
                                            //  Webcast 
                                            <div class="row-data-content">
                                                <div class="cell @(q1WebcastsFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q1WebcastsFile" title="@year"
                                       target="@(q1WebcastsFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q1")</a>
                                                </div>
                                                <div class="cell @(q2WebcastsFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q2WebcastsFile" title="@year"
                                       target="@(q2WebcastsFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q2")</a>
                                                </div>
                                                <div class="cell @(q3WebcastsFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q3WebcastsFile" title="@year"
                                       target="@(q3WebcastsFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q3")</a>
                                                </div>
                                                <div class="cell @(q4WebcastsFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q4WebcastsFile" title="@year"
                                       target="@(q4WebcastsFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q4")</a>
                                                </div>
                                            </div>

                                        }
                                        if (i == 2)
                                        {

                                            //SEC Filing
                                            <div class="row-data-content">
                                                <div class="cell @(q1SECFilingFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q1SECFilingFile" title="@year"
                                       target="@(q1SECFilingFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q1")</a>
                                                </div>
                                                <div class="cell @(q2SECFilingFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q2SECFilingFile" title="@year"
                                       target="@(q2SECFilingFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q2")</a>
                                                </div>
                                                <div class="cell @(q3SECFilingFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q3SECFilingFile" title="@year"
                                       target="@(q3SECFilingFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q3")</a>
                                                </div>
                                                <div class="cell @(q4SECFilingFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q4SECFilingFile" title="@year"
                                       target="@(q4SECFilingFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q4")</a>
                                                </div>
                                            </div>
                                        }
                                        if (i == 3)
                                        {
                                            //   HKEX Filing
                                            <div class="row-data-content">
                                                <div class="cell @(q1HKEXFilingFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q1HKEXFilingFile" title="@year"
                                       target="@(q1HKEXFilingFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q1")</a>
                                                </div>
                                                <div class="cell @(q2HKEXFilingFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q2HKEXFilingFile" title="@year"
                                       target="@(q2HKEXFilingFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q2")</a>
                                                </div>
                                                <div class="cell @(q3HKEXFilingFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q3HKEXFilingFile" title="@year"
                                       target="@(q3HKEXFilingFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q3")</a>
                                                </div>
                                                <div class="cell @(q4HKEXFilingFile.Equals("javascript:void(0);")==false ? "file-avaliable" : "")">
                                                    <a href="@q4HKEXFilingFile" title="@year"
                                       target="@(q4HKEXFilingFile.Equals("javascript:void(0);")==false ? "_blank" : "")">@Umbraco.GetDictionaryValue("Q4")</a>
                                                </div>
                                            </div>
                                        }
                                    }
                                </div>
                            </div>
                        }
                    </div>

                </div>
            </div>
            <div class="last-right">
                <div class="right-sec"></div>
                <div class="right-sec"></div>
                <div class="right-sec"></div>
                <div class="right-sec"></div>
                <div class="right-sec"></div>
            </div>
        </div>
    }
</div>