@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;
@{
  var carousel = getBannerCarousel();
  var home = Model.Root();
  var pageTitle = Model.Value<string>("pageTitle");
  bool IsHomePage = Model.ContentType.Alias.ToLower().Equals("homepage")
  ? true
  : (Model.HasProperty("isIrSite") && Model.HasValue("isIrSite") && Model.Value<bool>("isIrSite") ?
  Model.ContentType.Alias.ToLower().Equals("investorrelationspage") : false);
  bool IsChildPage = !IsHomePage;
  bool hiddenBanner = Model.HasProperty("hiddenBanner") && Model.HasValue("hiddenBanner") ?
  Model.Value<bool>("hiddenBanner") : false;
  string imageBannerDefault = "/media/xlabimpw/banner1.png";
  var textEffect = Model.HasProperty("textEffect") && Model.HasValue("textEffect") ?
  Model.Value<string>("textEffect").ToLower().Replace(" ", "-").Replace("'", "-") : "animate__fadeInUp";

  if (textEffect.Equals("faded-up")) { textEffect = "animate__fadeInUp"; }
  if (textEffect.Equals("faded-down")) { textEffect = "animate__fadeInDown"; }
  if (textEffect.Equals("faded-right-to-left")) { textEffect = "animate__fadeInRight"; }
  if (textEffect.Equals("faded-left-to-right")) { textEffect = "animate__fadeInLeft"; }

  string dragSlider = Model.HasProperty("dragSlider") && Model.HasValue("dragSlider")
  ? Model.Value<string>("dragSlider").ToLower().Replace(" ", "-").Replace("'", "-") : "false";
  string effectSlider = Model.HasProperty("effectSlider") && Model.HasValue("effectSlider")
  ? Model.Value<string>("effectSlider").ToLower().Replace(" ", "-").Replace("'", "-") : "none";
  string enableDot = Model.HasProperty("enableDot") && Model.HasValue("enableDot")
  ? Model.Value<string>("enableDot").ToLower().Replace(" ", "-").Replace("'", "-") : "false";
  string enableArrow = Model.HasProperty("enableArrow") && Model.HasValue("enableArrow")
  ? Model.Value<string>("enableArrow").ToLower().Replace(" ", "-").Replace("'", "-") : "false";

  string timingEachSlide = Model.HasProperty("timingEachSlide") && Model.HasValue("timingEachSlide")
  ? Model.Value<string>("timingEachSlide").ToLower().Replace(" ", "-").Replace("'", "-") : "5000";
  string bannerHeight = Model.HasProperty("bannerHeight") && Model.HasValue("bannerHeight")
  ? Model.Value<string>("bannerHeight").ToLower().Replace(" ", "-").Replace("'", "-") : "40";
  string bannerFullPage = Model.HasProperty("bannerFullPage") && Model.HasValue("bannerFullPage")
  ? Model.Value<string>("bannerFullPage").ToLower().Replace(" ", "-").Replace("'", "-") : "false";

  var unit = "rem";

  if (timingEachSlide == "0")
  {
    timingEachSlide = "5000";
  }

  if (bannerHeight == "0")
  {
    bannerHeight = "40";
  }

  string cssBannerHeight = bannerHeight + unit;
  var arrowDown = "/media/zdsls5xs/arrow-down.png";
}

@* Define style setting *@
<style>
  .banner-wrap.banner-fullpage-false .swiper-slide {
    min-height: @cssBannerHeight;
    max-height: @cssBannerHeight;
  }
</style>

@functions {
  public IEnumerable<IPublishedElement> getBannerCarousel()
  {
    var carousel = new List<IPublishedElement>();
    int level = Model.Level;
    IPublishedContent currentPage;

    // Keep travelling up the tree node to get banner carousel:
    while (level > 0)
    {
      currentPage = Model.AncestorOrSelf(level);

      if (currentPage.HasProperty("bannerCarousel") &&
      currentPage.HasValue("bannerCarousel") &&
      currentPage.Value("bannerCarousel") != null)
      {
        carousel = currentPage.Value<IEnumerable<IPublishedElement>>("bannerCarousel").ToList();
        break;

      }
      level--;
    }

    return carousel;
  }
}

@if (hiddenBanner)
{
  return;
}

<!-- banner -->
<div
  class="swiper mySwiper banner-wrap banner-swiper dot-@enableDot arrow-@enableArrow banner-fullpage-@bannerFullPage">
  <div class="swiper-wrapper">
    @if (carousel.Any())
    {

      foreach (var item in carousel)
      {
        var photoUrl = (item.HasProperty("image") && item.HasValue("image")) ? item.Value<IPublishedContent>("image")?.Url()
        : imageBannerDefault;
        var title = item.HasProperty("title") && item.HasValue("title") ? item.Value<string>("title") : "";
        var secondLineTitle = item.HasProperty("secondLineTitle") && item.HasValue("secondLineTitle") ?
        item.Value<string>("secondLineTitle") : "";
        //pageTitle= title -> pageTitle
        pageTitle = title.Length > 0 ? title : pageTitle;
        var hiddenCaption = item.HasProperty("hiddenCaption") && item.HasValue("hiddenCaption") ?
        item.Value<bool>("hiddenCaption") : true;
        var useParentTitle = item.HasProperty("useParentTitle") && item.HasValue("useParentTitle") ?
        item.Value<bool>("useParentTitle") : false;

        if (useParentTitle)
        {
          pageTitle = Model.Parent.Value<string>("pageTitle");
        }

        var quickLink = item.Value<Link>("linkUrl");
        var quickLinkUrl = item.Value<Link>("linkUrl")?.Url ?? "";
        var quickLinkName = item.Value<Link>("linkUrl")?.Name ?? "";

        var linkPinterest = item.Value<Link>("linkPinterest")?.Url ?? "";
        var linkPinterestName = item.Value<Link>("linkPinterest")?.Name ?? "";

        var linkFacebook = item.Value<Link>("linkFacebook")?.Url ?? "";
        var linkFacebookName = item.Value<Link>("linkFacebook")?.Name ?? "";

        var linkInstagram = item.Value<Link>("linkInstagram")?.Url ?? "";
        var linkInstagramName = item.Value<Link>("linkInstagram")?.Name ?? "";

        string content = item.HasProperty("content") && item.HasValue("content") ? item.Value<String>("content") : "";

        string videoUrl = item.HasProperty("video") && item.HasValue("video") ?
        item.Value<IPublishedContent>("video")?.Url() ?? "" : "";
        @* var videoUrl = item.HasProperty("video") && item.HasValue("video") ?
    item.Value<IPublishedContent>("video")?.Url()


    ?? "" : ""; *@

        string iconQR = home.Value<IPublishedContent>("iconQR")?.Url() ?? "";


        if (hiddenCaption || IsChildPage)
        {
          //disable page title
          content = quickLinkUrl = "";
        }
        generateBannerItem(
        photoUrl,
        pageTitle,
        quickLink,
        quickLinkUrl,
        quickLinkName,
        linkPinterest,
        linkPinterestName,
        linkFacebook,
        linkFacebookName,
        linkInstagram,
        linkInstagramName,
        content,
        videoUrl,
        textEffect,
        dragSlider,
        effectSlider,
        timingEachSlide,
        iconQR)
        ;
      }
    }
  </div>
  <div class="swiper-button-next"></div>
  <div class="swiper-button-prev"></div>
  <div class="swiper-pagination"></div>

  <img class="arrow-down" src="@arrowDown" alt="img">
</div>
<!--ticker for all banner images-->


@functions {
  private void generateBannerItem(
  string photoUrl,
  string pageTitle,
  Link quickLink,
  string quickLinkUrl,
  string quickLinkName,
  string linkPinterest,
  string linkPinterestName,
  string linkFacebook,
  string linkFacebookName,
  string linkInstagram,
  string linkInstagramName,
  string content,
  string videoUrl,
  string textEffect,
  string dragSlider,
  string effectSlider,
  string timingEachSlide,
  string iconQR)
  {

    if (!string.IsNullOrEmpty(@videoUrl))
    {
      <div class="swiper-slide background-video" data-drag-slider="@dragSlider" data-effect-slider="@effectSlider"
        data-timing="@timingEachSlide">
        <video id="myVideo" autoplay="autoplay" loop muted playsinline>
          <source src="@videoUrl" type="video/mp4">
          @* <source src="/media/i0glr3sr/pkd64i9pusg2m4cjd6s1668756130298.m4v" type="video/mp4"> *@

        </video>
        <div class="container">
          <div class="wrap-content">
            <div class="content animate__animated @textEffect">
              @{
                <h2 class="banner-title-homepage first-line">@Html.Raw(pageTitle)</h2>
                <div class="content-description">@Html.Raw(content)</div>
                if (!string.IsNullOrEmpty(quickLinkUrl))
                {
                  <a class="medium button btn-beauty" href="@quickLinkUrl" target="@quickLink.Target">@quickLinkName</a>
                }
                @* <div class="qr-btn-mobile">
        <img src="@iconQR" alt="@Umbraco.GetDictionaryValue("QR Code")">
        </div> *@
              }
            </div>
          </div>
        </div>
      </div>
    }
    else
    {
      <div class="swiper-slide" style="background-image: url('@photoUrl');" data-drag-slider="@dragSlider"
        data-effect-slider="@effectSlider" data-timing="@timingEachSlide">
        <div class="container">
          <div class="wrap-content">
            <div class="content animate__animated @textEffect">
              @{
                <h2 class="banner-title-homepage first-line">@Html.Raw(pageTitle)</h2>
                <div class="content-description">@Html.Raw(content)</div>
                if (!string.IsNullOrEmpty(quickLinkUrl))
                {
                  <a class="medium button btn-beauty" href="@quickLinkUrl" target="@quickLink.Target">@quickLinkName</a>
                }
              }
            </div>
          </div>
        </div>
      </div>
    }
  }
}