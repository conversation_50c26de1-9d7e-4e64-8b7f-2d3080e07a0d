﻿using Newtonsoft.Json;
using System.Net;
using System.Text.Json;
using System.Text.Json.Serialization;
using Umbraco.Cms.Core;

namespace umb_bilibili
{
    public class Json
    {
        public static List<News> Deserialize(string jsonUrl)
        {
            try
            {
                if (string.IsNullOrEmpty(jsonUrl))
                {
                    
                }
                else
                {
                    using (WebClient client = new WebClient())
                    {
                        string s = client.DownloadString(jsonUrl);
                        dynamic dnews = JsonConvert.DeserializeObject<dynamic>(s);
                        List<News> listNews = new List<News>();
                        foreach (dynamic d in dnews)
                        {
                            News news = new News();
                            news.Title = d.title;
                            news.PublishedDate = d.date;
                            news.Url = d.link;
                            listNews.Add(news);
                        }
                        return listNews;
                    }
                   
                }
            }
            catch
            {
               
            }
            return null;
        }
    }
}
