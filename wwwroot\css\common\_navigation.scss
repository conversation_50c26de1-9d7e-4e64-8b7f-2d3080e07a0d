.menu-header {
  height: 100%;

  > .ul-menu {
    display: flex;
    align-items: center;
    height: 100%;

    > li {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 15px;

      @include transition;

      &:hover {
        a {
          animation: textDance 200ms ease-in-out;
        }
      }

      > a {
        color: #fff;
        font-size: 1.8rem;
        line-height: 1.3;
        @include transition();
        // font-family: "Sofia Pro";
        font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
        @media (min-width: 1201px) and (max-width: 1400px) {
          font-size: 1.6rem;
        }

        @media (min-width: 1401px) and (max-width: 1529px) {
          font-size: 1.7rem;
        }

        .base-sticky.headroom--unpinned &,
        .base-sticky.headroom--pinned.headroom--not-top & {
          color: #0d1c21;
        }
      }

      &:first-child {
        padding-left: 0;
      }

      &:last-child {
        padding-right: 0;
      }

      @media (max-width: 1600px) {
        padding: 0 20px;
      }
      @media (max-width: 1400px) {
        padding: 0 10px;
      }
      @media (min-width: 1201px) and (max-width: 1400px) {
        padding: 0 7px;
      }
      @media (min-width: 1401px) and (max-width: 1529px) {
        padding: 0 10px;
      }
    }

    li.level-3 ul {
      display: none !important;
    }
  }

  .menu-pc {
    > li > ul {
      max-width: 23rem;
    }

    > li li .open-sub {
      display: none;
    }

    li.level-4 li {
      display: none;
    }
  }

  .multi-level.level-2 {
    position: relative;

    > .ul-menu {
      display: none;
      position: absolute;
      // top: calc(100% + 2rem);
      top: 100%;
      left: 0;
      background-color: rgba(255, 255, 255, 0.7);
      backdrop-filter: saturate(180%) blur(20px);
      border: none;
      width: max-content;
      min-width: 20rem;
      padding: 1rem 0;
      z-index: 2;
      @include transition();

      > li {
        padding: 12px 20px;
        @include transition();

        a {
          color: #0a2333;
          line-height: 1.3;
          @include transition();
        }

        &:hover {
          background-color: $colorBlue;

          a {
            color: #fff;
          }
        }
      }

      li.level-3 {
        position: relative;

        .ul-menu {
          position: absolute;
          top: 0;
          left: 100%;
          width: 200px;
          background-color: #fff;
          transform: translateY(10px);
          @include transition();
          opacity: 0;
          visibility: hidden;

          > li {
            padding: 12px 20px;
            @include transition();

            a {
              color: #0a2333;
              @include transition();
            }

            &:hover {
              background-color: #fff;
            }
          }
        }

        &:hover {
          .ul-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
          }
        }
      }
    }

    &:hover {
      .ul-menu {
        display: block;
      }
    }

    > .open-sub {
      display: inline-block;
      width: 0;

      i {
        font-size: 1.2rem;
        color: #fff;
      }

      @media (min-width: 1200px) {
        display: none;
      }
    }
  }
}

.header.menu-mobile-dropdown .menu-mobile {
  overflow: hidden;
  box-shadow:
    0 3px 6px rgba(0, 0, 0, 0.16),
    0 3px 6px rgba(0, 0, 0, 0.23);

  > ul.menu-responsive {
    overflow-y: scroll;
  }

  .menu-responsive {
    display: none;
    padding: 0;
    width: 100%;
    list-style: none;
    background-color: $colorBlue;
    position: relative;
    height: 100vh;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */

    &::-webkit-scrollbar {
      display: none;
    }

    ul {
      float: none;
      height: auto;
    }

    li.back {
      display: none;
    }

    li.level-3 {
      .open-sub,
      ul {
        display: none;
      }
    }

    .back-container {
      display: none;
    }

    .ul-menu {
      padding: 0 0 0 1rem;
      list-style: none;
      display: none;
    }

    > .menu-item {
      position: relative;
      padding: 0 3rem;

      &:not(.hassub) {
        .open-sub {
          display: none;
        }
      }

      &.hassub {
        .open-sub {
          position: absolute;
          top: 0.5rem;
          right: 3rem;
          width: 4.5rem;
          height: 4.5rem;
          transform-origin: top left;

          i {
            position: absolute;
            color: #fff;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.5s ease-in-out;
          }
        }

        &.open {
          > .open-sub i {
            transform: translate(-50%, -50%) rotate(-180deg);
          }
        }
      }
    }

    a {
      display: block;
      width: 100%;
      font-size: 16px;
      line-height: 5rem;
      text-align: left;
      color: #fff;
      position: relative;
      border-bottom: 0.1rem solid #eee;
    }

    @media (max-width: 600px) {
      margin: 0 -1.5rem;
      width: calc(100% + 3rem);
    }
  }

  @media (max-width: 1025px) {
    margin: 0 -30px;
  }
  @media (max-width: 600px) {
    margin: 0 -15px;
  }
}

.header.menu-mobile-off-canvas .menu-mobile .menu-responsive {
  padding: 0;
  width: 100%;
  list-style: none;
  background-color: #202020;
  position: fixed;
  height: 100%;
  left: 0;
  padding-top: 1rem;
  transform: translateX(100%);
  transition: all 0.5s ease-in-out;
  z-index: 10;

  &.active {
    transform: translateX(0);
  }

  ul {
    float: none;
  }

  .back {
    position: relative;
    background-color: #202020;

    a {
      color: #fff !important;
      font-size: 1.4rem;
      padding-left: 3rem;
    }

    svg {
      position: absolute;
      top: 50%;
      left: 2rem;
      transform: translate(-50%, -50%);
      transition: all 0.5s ease-in-out;
    }
  }

  .ul-menu {
    padding: 0 0 0 1rem;
    list-style: none;
    position: fixed;
    width: 100%;
    top: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #202020;
    left: 0;
    transform: translateX(100%);
    transition: all 0.5s ease-in-out;
    z-index: 1;
  }

  > .menu-item {
    position: relative;

    &:not(.hassub) {
      .open-sub {
        display: none;
      }
    }

    &.hassub {
      .open-sub {
        position: absolute;
        top: 0;
        right: 0;
        width: 40px;
        height: 40px;

        i {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) rotate(-90deg);
          transition: all 0.5s ease-in-out;
        }
      }

      &.open {
        .ul-menu {
          transform: translateX(0);
        }
      }
    }
  }

  a {
    display: block;
    width: 100%;
    font-size: 1.4rem;
    padding: 0 1.5rem;
    line-height: 5rem;
    text-align: left;
    color: #fff;
    position: relative;
    text-decoration: none;
    border-bottom: 0.1rem solid rgba(255, 255, 255, 0.3);

    span {
      color: #fff;
    }
  }
}

.header body {
  position: relative;

  &.open-menu {
    .background-opacity {
      opacity: 1;
      visibility: visible;
    }
  }

  &.open-offcanvas {
    .off-canvas {
      opacity: 1;
      visibility: visible;
      -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
      transform: translateX(0);
    }
  }
}

.menu-manufacturing,
.menu-manufacturing-facility {
  .ul-menu {
    .menu-item {
      &:nth-child(1),
      &:nth-child(2) {
        display: none;
      }
    }
  }
}
