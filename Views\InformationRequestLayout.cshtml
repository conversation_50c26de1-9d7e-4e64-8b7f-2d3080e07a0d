﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
	@* Layout = "ChildPageLayout.cshtml"; *@
	var informationRequestPage = Model.DescendantsOfType("informationRequestPage")?.FirstOrDefault() ?? null;
	var request = informationRequestPage != null? informationRequestPage.Value("request"): "";
}

<div class="information-request-container">
	<div class="request">
		<p class="text-font-size-24">@request</p>
		<p class="text-font-size-14">@Umbraco.GetDictionaryValue("Required fields denoted by an asterisk (*).")</p>
	</div>
	<div class="information-request-form">
		@{
            Html.RenderPartial("Layout/ContactForm");
        }
	</div>
</div>