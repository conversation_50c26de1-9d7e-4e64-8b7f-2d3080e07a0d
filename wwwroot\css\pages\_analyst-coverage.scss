.analyst-coverage-container {
    @media (min-width: 320px) and (max-width: 767px) {
        .box-table {
            overflow-x: scroll;
            overflow-y: hidden;
            white-space: nowrap;
            width: auto;
        }
    }
    .table {
        > thead {
            background: #0cb6f2;
            > tr {
                th {
                    color: #fff;
                    border-bottom: 0;
                    vertical-align: middle;
                    padding: 1.8rem 0 1.8rem 12rem;
                    width: calc(100% / 3);
                    border-top: 0;
                    @media (min-width: 768px) and (max-width: 1025px) {
                        padding: 1.8rem 0 1.8rem 1.8rem;
                    }
                    @media (min-width: 320px) and (max-width: 767px) {
                        padding: 1rem 0 1rem 3.8rem;
                    }
                    &:last-child {
                        //padding: 1.8rem 0 .8rem 4rem;
                        padding: 1.8rem 0 1.8rem 12rem;
                        @media (min-width: 768px) and (max-width: 1025px) {
                            padding: 1.8rem 0 1.8rem 1.8rem;
                        }
                        @media (min-width: 320px) and (max-width: 767px) {
                            padding: 1rem 1rem 1rem 3.8rem;
                        }
                    }
                }
            }
        }
        > tbody {
            > tr {
                > td {
                    border-top: 0;
                    padding: 1.8rem 0 1.8rem 12rem;
                    // font-family: "Sofia Pro";
                    font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
                    @media (min-width: 768px) and (max-width: 1025px) {
                        padding: 1.8rem 0 1.8rem 1.8rem;
                        font-size: 1.6rem;
                    }
                    @media (min-width: 320px) and (max-width: 767px) {
                        padding: 1rem 0 1rem 3.8rem;
                        vertical-align: middle;
                        font-size: 1.4rem;
                    }
                    &:last-child {
                        //padding: 1.8rem 0 1.8rem 4rem;
                        padding: 1.8rem 0 1.8rem 12rem;
                        @media (min-width: 768px) and (max-width: 1025px) {
                            padding: 1.8rem 0 1.8rem 1.8rem;
                        }
                        @media (min-width: 320px) and (max-width: 767px) {
                            padding: 1rem 1rem 1rem 3.8rem;
                        }
                    }
                    a {
                        color: #0cb6f2;
                        text-decoration: underline;
                    }
                }
                &:nth-child(odd) {
                    background: #fff;
                }
                &:nth-child(even) {
                    background: #0d1c210a;
                }
            }
        }
    }

    .analyst-info {
        margin-top: 5rem;

        @media (min-width: 320px) and (max-width: 767px) {
            margin-top: 3rem;
        }

        p {
            font-size: 1.4rem;
            color: #0d1c218a;
        }
    }
}
