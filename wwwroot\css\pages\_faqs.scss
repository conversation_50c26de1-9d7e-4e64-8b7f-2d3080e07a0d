﻿.faqs-container {
    .item {
        margin-bottom: 1rem;
        &.active {
            .head-info {
                .question {
                    color: #ffffff;
                }
            }
        }

        .head-info {
            position: relative;
            cursor: pointer;
            //background: rgba(13, 28, 33, 0.26);
            background: #eeeeee4d;
            height: 6rem;
            display: flex;
            align-items: center;
            padding-left: 4rem;

            @media screen and (max-width: 767px) {
                height: auto;
                padding: 1rem 5rem 1rem 2rem;
            }

            .question {
                font-weight: 500;
                font-size: 1.6rem;
                line-height: 150%;
                //color: #ffffff;
                color: $colorBlue;
            }

            .arrow {
                position: absolute;
                top: 2rem;
                right: 2.4rem;

                @media screen and (max-width: 767px) {
                    //right:2rem;
                    top: 0;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                }

                .img-active {
                    display: none;
                }

                .img-icon {
                    transform: rotate(360deg);
                }

                // img {
                //     transform: rotate(180deg);
                // }
            }
        }

        .desc {
            font-weight: 400;
            font-size: 1.6rem;
            line-height: 150%;
            color: $colorGrey;
            display: none;
            padding: 2rem 4rem;

            @media (min-width: 320px) and (max-width: 480px) {
                font-size: 1.4rem;
                padding: 2rem;
            }
        }
    }

    .item.active {
        .head-info {
            background: $colorBlue;

            .arrow {
                .img-icon {
                    display: none;
                }

                .img-active {
                    display: block;
                    transform: revert;
                }
                // img {
                //     transform: revert;
                // }
            }
        }
    }

    .box-header {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 5rem;
        @media (min-width: 200px) and (max-width: 767px) {
            flex-direction: column;
            margin-bottom: 0;
        }
        .tab-header {
            width: 22rem;
            border: 1px solid #ff558c;
            border-radius: 2rem;
            color: #ff558c;
            font-weight: 400;
            font-size: 1.6rem;
            line-height: 150%;
            text-align: center;
            padding: 0.7rem 1.5rem;
            // font-family: "Sofia Pro";
            font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
            margin: 0 2rem;
            @media (min-width: 200px) and (max-width: 767px) {
                margin: 0 0 2rem 0;
            }
            cursor: pointer;
            &.active {
                background-color: #ff558c;
                color: #fff;
                pointer-events: none;
            }
        }
    }

    .box-content {
        .box {
            display: none;
        }
    }
}

:lang(cn),
:lang(hk) {
    .faqs-container {
        .box-header {
            @media (min-width: 200px) and (max-width: 767px) {
                flex-direction: unset;
                margin-bottom: 2rem;
            }
            .tab-header {
                @media (min-width: 200px) and (max-width: 767px) {
                    width: 14rem;
                    margin: 0 1rem;
                }
            }
        }
    }
}
