function LoadBannerImages(e) {
  var slideItem = $(".banner-wrap .swiper-wrapper .swiper-slide"),
  dragSlider = slideItem.attr("data-drag-slider"),
  effectSlider = slideItem.attr("data-effect-slider"),
  timingEachSlide = slideItem.attr("data-timing"),
  dataSwiper = '{"dragSlider":"' + dragSlider + '",' + '"effectSlider":"' + effectSlider + '",' + '"timingEachSlide":"' + timingEachSlide + '"}',
  dataAttr = JSON.parse(dataSwiper);

  //there are more one item we will add the swiper
  if (slideItem && slideItem.length > 1) {
    var swiper = new Swiper(".banner-swiper", {
      slidesPerView: 1,
      spaceBetween: 0,
      speed: 1000,
      pagination: {
        el: ".swiper-pagination",
      },
      
      allowTouchMove: "true" == dataAttr.dragSlider,
      autoplay: {
        delay: dataAttr.timingEachSlide,
      },
      loop: true,
      navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
      effect: dataAttr.effectSlider,
      flipEffect: {
        slideShadows: true,
      },
      coverflowEffect: {
        rotate: 30,
        slideShadows: true,
      },
      cubeEffect: {
        slideShadows: true,
      },
      creativeEffect: {
        prev: {
          // will set `translateZ(-400px)` on previous slides
          translate: [0, 0, -400],
        },
        next: {
          // will set `translateX(100%)` on next slides
          translate: ['100%', 0, 0],
        },
      },
    });
  }
}

function ResizeBanner(maxWidth) {
  
  $window = $(window);  
  if  ($window.width() <= maxWidth){
    var $width = $window.width();
    var numberScale = 2.57;
    var bannerSlide = $('.swiper-slide:not(.banner-fullpage-true)')
    if ($('.childpage-content').hasClass('investor-relations')) {
      numberScale = 2.02;
    }
  
    $heightBanner = $width/numberScale;
    bannerSlide.css("height", $heightBanner);
  }
  else{
    bannerSlide.css("height","");   
  }
}

function BannerHeight(maxWidth){
  ResizeBanner(maxWidth);  
  $window.resize(function() {
    $window = $(window);
    if($window.width() <= maxWidth) {
      ResizeBanner(maxWidth);
    }
    else{
      $(".swiper-slide").css("height","");   
    }
  }); 
}

function HoverQR() {
  var $qrBtn = $('.qr-btn');
  $qrBtn.hover(function () {
    var $this = $(this);
    $this.closest('.qr-wrap').toggleClass('open-qr');
    $this.closest('.qr-wrap-footer').toggleClass('open-qr');
  });
}

function QRBannerMobile() {
  var $qrBtnMobile = $('.qr-btn-mobile');
  $qrBtnMobile.on('click', function() {
    $('body').addClass('open-qr-mobile');
  })

  $(document).click((event) => {
    if (!$(event.target).closest('.qr-btn-mobile').length && !$(event.target).is('.qr-btn-mobile') ) {
      $('body').removeClass('open-qr-mobile');
    }        
  });
}

$(document).ready(function () {
  
  var $w = $(window);
  var lastScrollTop = $w.scrollTop();
  var bannerTicket = $('.banner-ticket');

  HoverQR();

  if  ($w.width() <= 1600){
    LoadBannerImages();
    BannerHeight(1600);
  }

  if  ($w.width() <= 1025) {
    QRBannerMobile();
  }

  $(window).one('scroll', function() {
    var st = $w.scrollTop();
    var startPos = 1;
    lastScrollTop = st;
    
    if(lastScrollTop > 1 && $("#business-highlights").length > 0) {
      
      $('html, body').animate({
        scrollTop: $("#business-highlights").offset().top - 100
      }, 500);

    }

  });

  if($w.width() > 1025) {
    var swiper = new Swiper(".mySwiper", {
      direction: "vertical",
      autoHeight: true, //enable auto height
      slidesPerView: 'auto',  
      mousewheel: {
        forceToAxis: true,
        sensitivity: 0,
        releaseOnEdges: true,
      },
      speed: 500,
      on: {
        init: function() {
          $('.swiper-wrapper').removeClass('slide-active-1 slide-active-2 slide-active-3');
        },
        slideChangeTransitionEnd: function() {
          if($('.swiper-slide-active').hasClass('slide-1')) {
            $('.swiper-wrapper').removeClass('slide-1 slide-2 slide-3');
            $('.swiper-wrapper').addClass('slide-active-1');
          }
          if($('.swiper-slide-active').hasClass('slide-2')) {
            $('.swiper-wrapper').removeClass('slide-1 slide-2 slide-3');
            $('.swiper-wrapper').addClass('slide-active-2');
            $('.qr-wrap').fadeOut();
          }
        },

        reachEnd: function() {
          $('.swiper-wrapper').removeClass('slide-active-1 slide-active-2 slide-active-3');
          $('.swiper-wrapper').addClass('slide-active-3');
          
        },
        reachBeginning: function() {
            $('.swiper-wrapper').removeClass('slide-active-1 slide-active-2 slide-active-3');
            $('.qr-wrap').fadeIn();
        }
      },
      breakpoints: {
        // when window width is >= 1200px
        800: {
          on: {
            slideChangeTransitionEnd: function() {
              if($('.swiper-slide-active').hasClass('slide-2')) {
                $('.qr-wrap').fadeOut();
              }
            },
            reachBeginning: function() {
                $('.qr-wrap').fadeIn();
            }
          },
        }
      }
    });
  }
});

