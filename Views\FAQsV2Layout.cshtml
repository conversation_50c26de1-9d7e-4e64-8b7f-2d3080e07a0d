﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
    var investorFAQsPage = Model.DescendantsOfType("investorFAQsPage")?.FirstOrDefault() ?? null;
    var listQuestions = investorFAQsPage.Children.Where(x => x.IsVisible()).OrderBy(x => x.SortOrder).ToList();
    var arrowIcon = "/media/qsveavwl/microsoftteams-image-41.png";
    var arrowIconActive = "/media/u5ypktsm/up-arrow.png";
}
<div class="faqs-container">
    <div class="box-header">
        @if (listQuestions != null && listQuestions.Any())
        {
            var index = 0;
            @foreach (var item in listQuestions)
            {
                index ++;
                var faqTitle = item.Value("faqTitle").ToString();
                <div class="tab-header" data-button-count="@index">
                    <div class="title-tab">@faqTitle</div>
                </div>
            }
        }
        
    </div>
   

    <div class="box-content">
        @if (listQuestions != null && listQuestions.Any())
        {
            var index = 0;
            @foreach (var item in listQuestions)
            {
                index ++;
                var listFAQ = item.Children.Where(x => x.IsVisible()).OrderBy(x => x.SortOrder).ToList();

                <div class="box" data-content-count="@index">
                    @foreach (var _item in listFAQ)
                    {
                        var question = _item.Value("question").ToString();
                        var answer = _item.Value("answer").ToString();
                        <div class="item">
                            <div class="head-info">
                                <div class="question">
                                    @question
                                </div>
                                <div class="arrow">
                                    <img class="img-icon" src="@arrowIcon" alt="Arrow icon" />
                                    <img class="img-active" src="@arrowIconActive" alt="Arrow icon" />
                                </div>
                            </div>
                            <div class="desc">@Html.Raw(answer)</div>
                        </div>
                    }
                </div>
            }
        }
        
    </div>
</div>