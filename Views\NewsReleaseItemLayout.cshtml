﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage

@{
	Layout = "ChildPageIRLayout.cshtml";
	var pageTitle = Model.Value<String>("title");
	var content = Model.Value<String>("content");

	var parent = Model.Parent;
	var parentName = parent.Name.ToString().ToLower().Replace(" ", "-").Replace("'", "-");

	var parentUrl = parent.Parent.Url();
	IEnumerable<dynamic> listNews = parent.Children()
	.Where(x => x.IsVisible())
	.OrderByDescending(x => x.Value("publishedDate"))
	.ThenByDescending(x => x.SortOrder); ;

	var publishedDate = Model.Value<DateTime>("publishedDate") != null ?
	Model.Value<DateTime>("publishedDate") : DateTime.Now;
	var date = publishedDate.ToString(@Umbraco.GetDictionaryValue("Date Format - MM/dd/yyyy"));

	List<int> ids = listNews.Select(x => (int)x.Id).ToList();
	int indexOfThisPost = ids.IndexOf(Model.Id);
	bool isLast = (indexOfThisPost >= ids.Count() - 1);
	bool isFirst = indexOfThisPost == 0;

	string olderUrl = "";
	string newerUrl = "";

	var host = Context.Request.Host;
	var path = Context.Request.Path;

	var iconNext = "/media/c1ubdrl1/arrow-right.png";
	var iconPrev = "/media/p4vmcucu/arrow-left.png";
	var backIconUrl = "/media/ljlnzdrg/back-icon.png";
	var pdfIcon = "/media/0xzbdlyq/pdf-icon.png";

	var facebookIcon = "/media/jlopxg1x/facebookfacebook52-1.png";
	var twitterIcon = "/media/vw3hqzda/vector-1.png";
	var linkedinIcon = "/media/2igdhbok/linkedin-1.png";
	var weiboIcon = "/media/yfblgiui/vector.png";

	var pdfFile = (Model.HasValue("pDFFile") && Model.Value<IPublishedContent>("pDFFile") != null) ?
	Model.Value<IPublishedContent>("pDFFile").Url() : "";

	var devLink = "dev.vn.euroland.com:8086";
}

<div class="news-item-container container">
	<div class="news-release-content">
		<div class="parent-link">
			<a href="@parentUrl" target="_self">
				<img src="@backIconUrl" alt="" />
				@Umbraco.GetDictionaryValue("Back", "Back")
			</a>
		</div>

		<div class="title">
			<h3>@pageTitle</h3>
			<p>@date</p>
		</div>

		<div class="content">@Html.Raw(content)</div>

		<div class="navigator">
			@if (!isFirst)
			{
				var olderPage = Umbraco.Content(ids[indexOfThisPost - 1]);
				olderUrl = olderPage.Url();

				<div class="nav nav-prev">
					<a href="@olderUrl" class="navi-prev">
						<img src="@iconPrev" alt="">
						@Umbraco.GetDictionaryValue("Previous")
					</a>
				</div>
			}

			@if (!isLast)
			{
				var newerPage = Umbraco.Content(ids[indexOfThisPost + 1]);
				newerUrl = newerPage.Url();

				<div class="nav nav-prev"></div>

				<div class="nav nav-next">
					<a href="@newerUrl" class="navi-next">@Umbraco.GetDictionaryValue("Next")
						<img src="@iconNext" alt="">
					</a>
				</div>
			}
			else {
				<div class="nav nav-prev"></div>
			}
		</div>
	</div>

	<div class="share-section">
		<div class="pdf">
			<a href="@pdfFile" title="">
				<img class="pdf-icon" src="@pdfIcon" alt="">
				@Umbraco.GetDictionaryValue("View PDF Version")
			</a>
		</div>

		<div class="share">
			<p>@Umbraco.GetDictionaryValue("Share")</p>

			<div class="linkerdin share-icon">
				<a href="https://www.linkedin.com/sharing/share-offsite/?url=https://@host@path" target="_blank"
					rel="noopener noreferrer">
					<img src="@linkedinIcon" alt="">
				</a>
			</div>

			<div class="fb-share-buttonhaha share-icon facebook" 
			data-href="http://dev.vn.euroland.com:8086/en/news-and-events/news-releases/bilibili-inc-announces-results-of-annual-general-meeting-5/#" 
			data-layout="button" data-size="small">
				<a target="_blank" href="https://www.facebook.com/sharer/sharer.php?u=http%3A%2F%2F@(host+path)" 
						class="fb-xfbml-parse-ignore">
						<img src="@facebookIcon" alt="@pageTitle">
				</a>
			</div>

			<div class="twitter share-icon">
				<a 	href="https://twitter.com/intent/tweet?text=@pageTitle - https://@host@path"
						target="_blank"
						rel="noopener noreferrer" 
						data-size="large"
						data-show-count="false">
						<img src="@twitterIcon" alt="@pageTitle">
				</a>
			</div>

			<div class="weibo share-icon">
				<a href="#">
					<img src="@weiboIcon">
				</a>
			</div>
		</div>
	</div>
</div>
