@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;

@{
  Layout = null;
  var home = Model.Root();
  string companyLogo = home.Value<IPublishedContent>("companyLogo")?.Url() ?? "";
  var iCPUrl = home.Value<Link>("iCPUrl")?.Url ?? "#";
  var iCPName = home.Value<Link>("iCPUrl")?.Name ?? "";
  var copyright = home.Value("copyright");
  var footerQuickLinkItems = home.Value<IEnumerable<IPublishedElement>>("footerQuickLinkItems").ToList();

  string iconQR = home.Value<IPublishedContent>("iconQR")?.Url() ?? "";
  string imageQR = home.Value<IPublishedContent>("imageQR")?.Url() ?? "";
}

<div class="container">
  <div class="footer-wrapper">
    <div class="col-left">
      @* <img src="@companyLogo" class="logo-footer" alt="logo" /> *@
      <div class="quick-link">
        @if (footerQuickLinkItems != null && footerQuickLinkItems.Any())
        {
          foreach (var item in footerQuickLinkItems)
          {
            var title = item.Value("title");
            var linkUrl = item.Value<Link>("linkUrl")?.Url ?? "";
            <div class="ql-item">
              <a class="text-font-size-14" href="@linkUrl" target="_blank" title="@item.Value("title")">@title</a>
            </div>
          }
        }
      </div>
      @* <div class="qr-wrap-footer">
      <img class="qr-btn" src="@iconQR" alt="@Umbraco.GetDictionaryValue("QR Code")">
      <img class="qr-image" src="@imageQR" alt="@Umbraco.GetDictionaryValue("QR Code")">
      </div>

      <div class="qr-btn-mobile">
      <img src="@iconQR" alt="@Umbraco.GetDictionaryValue("QR Code")">
      </div> *@
    </div>

    <div class="col-right">
      <div class="copyright">
        @* <p>@copyright</p> *@

        @* <a title="ICP Link" target="_blank" class="icp-link" href=@iCPUrl>@iCPName</a> *@

        @if (!home.Value<bool>("hiddenDevelopedBy"))
        {
          var developedLink = home.Value<Link>("developedLink")?.Url ?? "#";
          <span class="developed-by">
            <a title="" target="_blank" href=@developedLink>@Umbraco.GetDictionaryValue("Developed by Euroland IR")
            </a>
          </span>
        }
      </div>
    </div>
  </div>
</div>
