﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;

@{
    var highlightTitle = Model.Value("highlightTitle");
    var businessCountItem = Model.Value<IEnumerable<IPublishedElement>>("businessCountItem").ToList();

    var types = Model.Value("type");
    var year = Model.Value("year");
}

<div class="container">
    <h3 class="title">@highlightTitle</h3>
    <div class="note-type">
        <p class="note-business">@year</p>
        <p class="note-business">@types</p>
    </div>
    @* <p class="note-business ">@Umbraco.GetDictionaryValue("In 2022Q1")</p> *@

    <div class="list-business-highlights" data-aos="fade-up" data-aos-duration="1000">
        @if (businessCountItem != null && businessCountItem.Any())
        {
            foreach (var item in businessCountItem)
            {
                var numberCount = item.Value<string>("numberCount");
                var tempnumberCount = numberCount;
                tempnumberCount = tempnumberCount.IndexOf(",") >= 0 ? tempnumberCount.Remove(tempnumberCount.IndexOf(",")) : tempnumberCount;
                var contentCount = item.Value("contentCount");
                var unit = item.Value("unit");

                <div class="item desktop">
                    <div class="box-count">
                        <div class="number-count count text-font-size-64" data-count="@tempnumberCount" data="@numberCount"></div>
                        <div class="unit-count">
                            @unit
                        </div>
                    </div>
                    <div class="content-count">
                        <p class="text-font-size-24">@contentCount</p>
                    </div>
                </div>

                <div class="item mobile">
                    <div class="box-count">
                        <div class="number-count text-font-size-64">@numberCount</div>
                        <div class="unit-count">
                            @unit
                        </div>
                    </div>
                    <div class="content-count">
                        <p class="text-font-size-24">@contentCount</p>
                    </div>
                </div>
            }
        }
    </div>
</div>