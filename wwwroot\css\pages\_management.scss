﻿.management-container {
    margin: 0 auto;

    @media screen and (min-width: 768px) {
        width: 68rem;
    }

    .person {
        margin-bottom: 2rem;
        padding: 4rem 4rem 4rem 6rem;
        background-color: $colorBlue;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;

        @media screen and (max-width: 767px) {
            gap: 1rem;
        }

        .info {
            .name {
                font-weight: 400;
                font-size: 2.4rem;
                line-height: 150%;
                color: #FFFFFF;
                margin-bottom: 1rem;

                @media (max-width: 600px) {
                    font-size: 1.8rem !important;
                }
            }

            .position {
                font-weight: 400;
                font-size: 1.6rem;
                line-height: 150%;
                color: #FFFFFF;
            }
        }
    }

    .management-modal {
        .modal-dialog {
            max-width: 100%;
            max-height: unset;
            top: 50%;
            left: 0;
            transform: translate(0, -50%);

            @media screen and (min-width: 992px) {
                max-width: 90rem;
                max-height: 58.1rem;
            }

            @media screen and (min-width: 1200px) {
                max-width: 104rem;
                max-height: 58.1rem;
            }

            .modal-content {
                .modal-header {
                    position: relative;
                    background-color: $colorBlue;
                    padding: 4rem 6rem 2rem 6rem;

                    .close {
                        position: absolute;
                        top: 3rem;
                        right: 3rem;
                        font-size: 2.4rem;
                        padding: 0;
                        margin: 0;
                    }

                    .head-info {
                        .name {
                            font-weight: 400;
                            font-size: 2.4rem;
                            line-height: 150%;
                            color: #FFFFFF;
                            margin-bottom: 1rem;
                        }

                        .position {
                            font-weight: 400;
                            font-size: 1.6rem;
                            line-height: 150%;
                            color: #FFFFFF;
                        }
                    }
                }

                .modal-body {
                    padding: 3rem 6rem;
                    max-height: 47rem;
                    overflow: scroll;

                    .info {
                        .desc {
                            font-weight: 400;
                            font-size: 1.6rem;
                            line-height: 150%;
                            color: $colorGrey;
                            margin-bottom: 2.5rem;

                            @media (min-width: 320px) and (max-width: 480px) {
                                font-size: 1.4rem;
                            }
                        }

                        .person-documents {
                            .documents-item {
                                display: flex;
                                gap: 2.3rem;
                                justify-content: left;
                                align-items: center;
                                margin-bottom: 2.1rem;

                                // .icon {
                                // }

                                .title {
                                    a {
                                        font-weight: 400;
                                        font-size: 1.6rem;
                                        line-height: 150%;
                                        color: #000000;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
