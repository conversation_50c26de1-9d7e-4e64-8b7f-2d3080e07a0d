@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;

@{
  Layout = null;
}

@{
  var home = Model.Root();
  string companyLogo = home.Value<IPublishedContent>("companyLogo")?.Url() ?? "";
  string logoSticky = home.Value<IPublishedContent>("logoSticky")?.Url() ?? "";
  string mobileLogo = home.Value<IPublishedContent>("mobileLogo")?.Url() ?? "";

  var homePageLogoUrl = home.HasProperty("homePageLogoUrl") && home.HasValue("homePageLogoUrl") ?
  home.Value<Link>("homePageLogoUrl").Url : home.Url();
  var menuItems = home.Children().Where(x => x.IsVisible()).ToList();
  var lang = System.Threading.Thread.CurrentThread.CurrentCulture.Name;
  var count = 0;
  var unit = "0px";
  //menuItems.Insert(0, home);

  // Setting value
  var logoWidth = home.HasProperty("logoWidth") && home.HasValue("logoWidth") ? home.Value<decimal>("logoWidth") : 23;
  var headerHeight = home.HasProperty("headerHeight") && home.HasValue("headerHeight") ?
  home.Value<decimal>("headerHeight") : 7;
  var logoWidthStyle = logoWidth + unit;
  var headerHeightStyle = headerHeight + unit;
  var backgroundHeader = home.Value<string>("backgroundHeader");
  var backgroundMenu = home.Value<string>("backgroundMenu");
  var colorMenu = home.Value<string>("colorMenu");
  var headerSticky = home.HasProperty("headerSticky") && home.HasValue("headerSticky")
  ? home.Value<string>("headerSticky").ToLower().Replace(" ", "-").Replace("'", "-") : "";
  var headerMobile = home.HasProperty("headerMobile") && home.HasValue("headerMobile")
  ? home.Value<string>("headerMobile").ToLower().Replace(" ", "-").Replace("'", "-") : "";
  var menuMobileStyle = home.HasProperty("menuMobileStyle") && home.HasValue("menuMobileStyle")
  ? home.Value<string>("menuMobileStyle").ToLower().Replace(" ", "-").Replace("'", "-") : "";
  var menuMobileLocation = home.HasProperty("menuMobileLocation") && home.HasValue("menuMobileLocation")
  ? home.Value<string>("menuMobileLocation").ToLower().Replace(" ", "-").Replace("'", "-") : "";
  var search = home.HasProperty("search") && home.HasValue("search") ? home.Value<string>("search").ToLower().Replace(" ",
  "-").Replace("'", "-") : "true";
  var language = home.HasProperty("language") && home.HasValue("language")
  ? home.Value<string>("language").ToLower().Replace(" ", "-").Replace("'", "-") : "";

  // Set default value
  if (string.IsNullOrEmpty(backgroundHeader)) { backgroundHeader = "transarent"; }
  if (string.IsNullOrEmpty(backgroundMenu)) { backgroundMenu = "#202020"; }
  if (string.IsNullOrEmpty(colorMenu)) { colorMenu = "#02A333"; }
  if (string.IsNullOrEmpty(headerSticky)) { headerSticky = "none"; }
}

@* Define style setting *@
<style>
  .logo-pc,
  .logo-mobile {
    max-width: @logoWidthStyle;
  }

  .header {
    height: @headerHeightStyle;
    background-color: @backgroundHeader;
  }

  .row-bottom {
    background-color: @backgroundMenu;
  }

  .row-bottom .menu-header>ul>li>a {
    color: @colorMenu;
  }
</style>

<div
  class="header @headerSticky header-mobile-@headerMobile menu-mobile-@menuMobileStyle menu-mobile-@menuMobileLocation">
  <div class="container">
    <div class="row row-top">

      <a href="@homePageLogoUrl" class="brand-logo" title="">
        <img src="@companyLogo" class="logo logo-on-top d-none d-sm-block logo-pc" alt="logo" />
        <img src="@logoSticky" class="logo logo-sticky d-none d-sm-block logo-pc" alt="logo" />
        @* <img src="@mobileLogo" class="logo d-block d-sm-none logo-mobile" alt="logo" /> *@
      </a>

      <div class="menu-header">
        @Html.Partial(
        "~/Views/Menu/CustomMenu.cshtml",
        new ViewDataDictionary(this.ViewData) {
        { "menuItems", menuItems },
        { "isFooter", false },
        { "isMobile", false }
        })
      </div>

      <div class="wrap-right">

        <div class="wrap-lang">
          @{
            generateLanguage(language, home);
          }
        </div>

        <div class="off-canvas">
          @{
            generateMenuOffCanvas();
          }
        </div>
      </div>
    </div>

    <div class="menu-mobile">
      @Html.Partial(
      "~/Views/Menu/CustomMenu.cshtml",
      new ViewDataDictionary(this.ViewData) {
      { "menuItems", menuItems },
      { "isFooter", false },
      { "isMobile", true }
      })
    </div>
  </div>
</div>

@functions {

  public bool isRedirectPage(IPublishedContent menuItem)
  {
    return (menuItem.ContentType.Alias == "redirectPage" || menuItem.ContentType.Alias == "redirectLandingPage");
  }

  private void generateMenuOffCanvas()
  {
    <div class="btn-menu-offcanvas">
      <label class="menuicon-label">
        <span class="line-top"></span>
        <span class="line-middle"></span>
        <span class="line-bottom"></span>
      </label>
      <i class="iconfont icon-guanbi"></i>
    </div>
  }

  private void generateLanguage(string language, IPublishedContent home)
  {
    if (language.Equals("true"))
    {
      @* <img class="global-white" src="/media/4ghlhgm5/global-white.png" alt="img" /> *@
      @* <img class="global-black" src="/media/4pae2ytz/global-black.png" alt="img" /> *@
      <div class="language d-flex">
        <div class="lang-list">
          @foreach (var (culture, infos) in home.Cultures)
          {
            <a class="@(home.Url() == home.Url(culture) ? "active" : "")" href="@(Model.Url(culture))">
              @Umbraco.GetDictionaryValue(("Language.") + culture)
            </a>
          }
        </div>
      </div>
    }
  }

  private void generateSearch(string search)
  {
    if (search.Equals("true"))
    {
      <div class="wrap-search-field">
        <div class="wrap-inner-search">
          <input type="text" placeholder="@Umbraco.GetDictionaryValue("Search")" class="search_box_input" id="formd_search"
        onkeypress="return enterKeyPressed(event)">
        </div>
      </div>
      <a class=" search_box_btn" id="btnSearch"><i class="iconfont icon-sousuo"></i></a>
    }
  }
}

<script>

  function enterKeyPressed(event) {
    if (event.keyCode == 13) {
      var $key = $('#formd_search').val();
      window.location.href = 'https://www.enn-ng.com/other/search.html?key=' + $key;
    }
  }
</script>
