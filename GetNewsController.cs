﻿using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;

namespace umb_bilibili
{
    public class GetNewsController : Controller
    {
        public static List<News> GetNewsByYear(string year, List<News> listNews)
        {
            List<News> listNewsByYear = new List<News>();
            if (!year.Equals("all-year"))
            {
                foreach (News item in listNews)
                {
                    if (item.PublishedDate.Year.ToString("yyyy").Equals(year))
                    {
                        listNewsByYear.Add(item);
                    }
                }
                Console.WriteLine("dasdsadasd");
                return listNewsByYear;
            }

            return listNews;
        }
        public IActionResult Index()
        {
            return View();
        }
    }
}
