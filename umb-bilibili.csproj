<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>umb_bilibili</RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <Content Remove="compilerconfig.json" />
  </ItemGroup>
  <ItemGroup>
    <None Remove=".gitignore" />
    <None Remove=".gitlab-ci.yml" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Our.Umbraco.TheDashboard" Version="10.0.1" />
    <PackageReference Include="Umbraco.Cms" Version="10.8.9" />
    <PackageReference Include="Umbraco.StorageProviders.AzureBlob" Version="10.0.1" />
  </ItemGroup>

  <!-- Force Windows to use ICU. Otherwise Windows 10 2019H1+ will do it, but older Windows 10 and most, if not all, Windows Server editions will run NLS -->
  <ItemGroup>
    <PackageReference Include="Microsoft.ICU.ICU4C.Runtime" Version="********" />
    <RuntimeHostConfigurationOption Include="System.Globalization.AppLocalIcu" Value="********" Condition="$(RuntimeIdentifier.StartsWith('linux')) or $(RuntimeIdentifier.StartsWith('win')) or ('$(RuntimeIdentifier)' == '' and !$([MSBuild]::IsOSPlatform('osx')))" />
  </ItemGroup>


  <PropertyGroup>
    <CopyRazorGenerateFilesToPublishDirectory>true</CopyRazorGenerateFilesToPublishDirectory>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="Views\Partials\Corporate Information\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="compilerconfig.json" />
  </ItemGroup>

  <!-- Keep this as false if ModelsBuilder mode is InMemoryAuto -->
  <PropertyGroup>
    <RazorCompileOnBuild>false</RazorCompileOnBuild>
    <RazorCompileOnPublish>false</RazorCompileOnPublish>
  </PropertyGroup>
</Project>
