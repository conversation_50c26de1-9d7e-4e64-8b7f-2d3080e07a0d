﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
	Layout = "ChildPageIRLayout.cshtml";
	var parentUrl = Model.Parent.Url();
	var backIcon = "/media/qifaepvs/rectangle-22810.png";
	@* var listReport = Model.Children
							.Where(x => x.IsVisible())
							.OrderByDescending(x => x.Value<DateTime>("publishedDate"))
							.ThenByDescending(x => x.SortOrder).ToList(); *@

	var eSGPage = Model.Root().DescendantsOfType("eSGPage")?.FirstOrDefault() ?? null;
    var eSGPageContent = eSGPage != null ?
    eSGPage.Children().OrderByDescending(x => x.Value("year")).ToList() : null;

	//var pageTitle = Model.Value("pageTitle").ToString();
}
<div class="esg-reports-container container">
	<div class="parent-back"><img src="@backIcon"/><a href="@parentUrl" title="Back to ESG" target="_self">@Umbraco.GetDictionaryValue("Back")</a></div>
	@if(eSGPageContent.Any()) {
		
			@foreach (var item in eSGPageContent)
			{
				var titleReport = item.Value("titleReport").ToString();
				var listReport = item.Children
							.Where(x => x.IsVisible())
							.OrderByDescending(x => x.Value<DateTime>("publishedDate"))
							.ThenByDescending(x => x.SortOrder).ToList();

				<div class="title-report">@titleReport</div>
				
				@if(listReport.Any()){
					<div class="list-report">
						@foreach (var _item in listReport)
						{
							var title = _item.Value("title").ToString();
							var photoUrl = "";
							if (_item.HasValue("photo") && _item.Value<IPublishedContent>("photo")!=null) {
								photoUrl = _item.Value<IPublishedContent>("photo").Url();
								photoUrl += "?width:438px";
								photoUrl += "?height:236px";
							}
							var fileUrl = "";
							if (_item.HasValue("file") && _item.Value<IPublishedContent>("file") != null)
							{
								fileUrl = _item.Value<IPublishedContent>("file").Url();
							}
							<div class="report">
								<div class="photo">
									<a href="@fileUrl" target="_blank" title="@title">
										<img src="@photoUrl" alt="@title"/>
									</a>
								</div>
								<div class="esg-report-title">
									<a href="@fileUrl" target="_blank" title="@title">@title</a>
								</div>
							</div>
						}
					</div>
				}
			}
	}
</div>