$(document).ready(function () {
    // $(".year-content").slideUp("slow","linear");
    // $(".show").slideDown("slow", "linear");
  // Select year
    $(".year-item").click(function () {
        $(".year-item").removeClass("active");
        $(this).addClass("active");
        $(".year-content").removeClass("show");
        $("#result").addClass("hide");
        var yearSelect = $(this).text().trim();
        $("#" + yearSelect + "-content").addClass("show");
        $(".year-content").slideUp("slow", "linear");
        //$(".show").slideDown("slow", "linear");
    });
    $("#all-news").click(function () {
        $(".year-item").removeClass("active");
        $(this).addClass("active");
        $(".year-content").removeClass("show");
        $("#result").addClass("hide");
        $("#all-year-content").addClass("show");
        $(".year-content").slideUp("slow", "linear");
        $(".show").slideDown("slow", "linear");
    });
    $("#results").click(function () {
        $(".year-item").removeClass("active");
        $(this).addClass("active");
        $(this).removeClass("hide");
        $(".year-content").removeClass("show");
        $("#search-results").addClass("show");
        $(".year-content").slideUp("slow", "linear");
        $(".show").slideDown("slow", "linear");
    });
  /* Search result */
  $(".search .submit-btn").click(function (e) {
    e.preventDefault();
    let searchUrl = "";
    let searchString = $("#key-word").val();
    let yearTabUrl = "&yearTab=results";
    let searchActionUrl = "&searchAction=true";
    var dateFrom = new Date($.now());

    if (searchString === "") {
      searchUrl = "?keyword=";
    } else {
      searchUrl = "?keyword=" + searchString;
    }
    if (!window.location.origin) {
      // For IE
      window.location.origin =
        window.location.protocol +
        "//" +
        (window.location.port ? ":" + window.location.port : "") +
        searchUrl +
        yearTabUrl +
        searchActionUrl;
    } else {
      window.location =
        window.location.origin +
        window.location.pathname +
        searchUrl +
        yearTabUrl +
        searchActionUrl;
    }
  });
});
