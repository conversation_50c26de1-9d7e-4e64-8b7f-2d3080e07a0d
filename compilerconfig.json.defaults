{"compilers": {"less": {"autoPrefix": "", "cssComb": "none", "ieCompat": true, "math": null, "strictMath": false, "strictUnits": false, "relativeUrls": true, "rootPath": "", "sourceMapRoot": "", "sourceMapBasePath": "", "sourceMap": false}, "sass": {"autoPrefix": "", "loadPaths": "", "style": "expanded", "relativeUrls": true, "sourceMap": false}, "nodesass": {"autoPrefix": "", "includePath": "", "indentType": "space", "indentWidth": 2, "outputStyle": "nested", "precision": 5, "relativeUrls": true, "sourceMapRoot": "", "lineFeed": "", "sourceMap": false}, "stylus": {"sourceMap": false}, "babel": {"sourceMap": false}, "coffeescript": {"bare": false, "runtimeMode": "node", "sourceMap": false}, "handlebars": {"root": "", "noBOM": false, "name": "", "namespace": "", "knownHelpersOnly": false, "forcePartial": false, "knownHelpers": [], "commonjs": "", "amd": false, "sourceMap": false}}, "minifiers": {"css": {"enabled": false, "termSemicolons": true, "gzip": false}, "javascript": {"enabled": false, "termSemicolons": true, "gzip": false}}}