﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{

    Layout = "MasterLayout.cshtml";

    var home = Model.Root();
    var language = System.Threading.Thread.CurrentThread.CurrentCulture.Name;
    var link = "";
    if (language.Equals("en-US"))
    {
        link = "/en/corporate-information/";
    }
    if (language.Equals("zh-Hans-CN"))
    {
        link = "/cn/corporate-information/";
    }
    if (language.Equals("zh-Hant-HK"))
    {
        link = "/hk/corporate-information/";
    }
    var title = Model.Value("pageTitle");
    var bannerTicket = home.Value("bannerTicket");
    var bgTicket = "/media/jcpakji3/bg-white.png";

    string iconQR = home.Value<IPublishedContent>("iconQR")?.Url() ?? "";
    string imageQR = home.Value<IPublishedContent>("imageQR")?.Url() ?? "";
}

@* the fun starts here *@

<div class="childpage-content  @Model.Name.ToLower().Replace(" ", "-").Replace("'", "-")">
    <div class="fluid-container">
        @if (@Model.Name.ToLower().Replace(" ", "-").Replace("'", "-") == "home")
        {
            <div class="wrap-slider-effect display-on-pc">
                <a class="link-hidden" href="@link"></a>
                <div class="swiper slider-vertical mySwiper">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide slide-1"></div>
                        <div class="swiper-slide slide-2">
                            <section class="banner-ticket" style="background: url('@bgTicket') top no-repeat">
                                <div class="ticket-wrap">
                                    <iframe class="eurolandtool" allowtransparency="" frameborder="0"
                                        id="iframe-ticket-banner" title="@Model.Value("pageTitle")" marginheight="0"
                                        marginwidth="0" scrolling="no" ea-src="@bannerTicket" width="100%">
                                    </iframe>
                                </div>
                            </section>
                        </div>
                        <div class="swiper-slide slide-3"></div>
                    </div>
                    <div class="swiper-pagination"></div>
                </div>
                @* <div class="qr-wrap">
            <img class="qr-btn" src="@iconQR" alt="@Umbraco.GetDictionaryValue("QR Code")">
            <img class="qr-image" src="@imageQR" alt="@Umbraco.GetDictionaryValue("QR Code")">
            </div> *@
            </div>
        }

        <section class="childpage-banner banner">
            @{

                Html.RenderPartial("Layout/Banner");
            }
        </section>

        @if (@Model.Name.ToLower().Replace(" ", "-").Replace("'", "-") == "home")
        {
            <div class="ticket-mobile  display-on-mobile">
                <iframe class="eurolandtool" allowtransparency="" frameborder="0" id="iframe-ticket-banner-mobile"
                    title="@Model.Value("pageTitle")" marginheight="0" marginwidth="0" scrolling="no" ea-src="@bannerTicket"
                    width="100%">
                </iframe>
            </div>
        }

        <div class="inner-content">

            @RenderBody()
        </div>
    </div>
</div>