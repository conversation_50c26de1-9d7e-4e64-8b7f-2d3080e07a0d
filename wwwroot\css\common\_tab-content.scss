.tab-head {
  border-bottom: 1px solid rgba(13, 28, 33, 0.26);
  
  .nav {
    gap: 5rem;
    justify-content: start;
    @media (min-width: 1026px) and (max-width: 1200px) {
      gap: 1rem;
    }

    .nav-item {
      text-align: center;

      @media (max-width: 1025px) {
        display: inline-block;
      }
    }

    .nav-link {
      position: relative;
      //font-size: 2rem;
      line-height: 1.5;
      color: #0D1C21;
      padding: 1.7rem 1.5rem;

      @media (min-width: 700px) and (max-width: 770px) {
        padding: 1.7rem 0.5rem;
        &.text-font-size-20 {
          font-size: 1.25rem !important;
        }
      }
      
      &:before {
        position: absolute;
        display: block;
        content: '';
        bottom: 0;
        left: 0;
        width: 0;
        height: 3px;
        background-color: #0CB6F2;

        @include transition();
      }
    
      &.active {
        &::before {
          width: 100%;
        }
      }
    }

    @media (min-width: 100px) and (max-width: 991px) {
      &::-webkit-scrollbar {
        display: none;
      }
    }

    @media (max-width: 1025px) {
      display: block;
      gap: 2rem;
      overflow-x: scroll;
      overflow-y: hidden;
      white-space: nowrap;
      width: auto;
      &::-webkit-scrollbar {
        //display: none;
      }
    }
  }
}   

.tab-content {
  margin-top: 10rem;
  margin-bottom: 10rem;

  // @media (min-width: 768px) and (max-width: 1280px) {
  //   margin-top: 5rem;
  // }
  
  @media (max-width: 767px)  { 
    margin-top: 5rem;
    margin-bottom: 0;
  }
}

