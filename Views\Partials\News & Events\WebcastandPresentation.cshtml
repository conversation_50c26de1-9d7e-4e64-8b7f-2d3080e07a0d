@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;

@{
  var home = Model.Root();
  var webcastPresentationPage = home.DescendantsOfType("webcastPresentation")?.FirstOrDefault() ?? null;
  var webcastContent = webcastPresentationPage.Children()
  .Where(x => x.IsVisible())
  .OrderByDescending(x => x.Value<DateTime>("publishedDate"))
  .ThenByDescending(x => x.SortOrder);

  var dateText = Umbraco.GetDictionaryValue("Date", "Date");
  var titleText = Umbraco.GetDictionaryValue("Title and Summary", "Title and Summary");
  var webcastText = Umbraco.GetDictionaryValue("Webcast", "Webcast");
  var presentationText = Umbraco.GetDictionaryValue("Presentation", "Presentation");

  var presentationIcon = "/media/p0cek0hi/icon-presentation.png";
  var webcastIcon = "/media/mwljjg2y/icon-webcast.png";
}

<div class="webcast-section">
  <div class="news-header">
    <div class="date-title ">
      <p class="text-font-size-20">@dateText</p>
    </div>

    <div class="summary-title">
      <p class="text-font-size-20">@titleText</p>
    </div>

    <div class="webcast-title">
      <p class="text-font-size-20">@webcastText</p>
    </div>

    <div class="presentation-title">
      <p class="text-font-size-20">@presentationText</p>
    </div>
  </div>

  <div class="news-content">
    @foreach (var content in webcastContent)
    {
      var title = content.Value<String>("title");

      var publishedDate = content.Value<DateTime>("publishedDate") != null ?
      content.Value<DateTime>("publishedDate") : DateTime.Now;
      var date = publishedDate.ToString(@Umbraco.GetDictionaryValue("Date Format - MM/dd/yyyy"));
      var dateV2 = publishedDate.ToString(@Umbraco.GetDictionaryValue("Date Format V2"));

      @* var webcast = content.HasProperty("webcast") && content.HasValue("webcast") ?
      content.Value<IPublishedContent>("webcast")?.Url() ?? "" : ""; *@

      var webcast = content.Value<Link>("webcast")?.Url;

      var presentation = content.HasProperty("presentation") && content.HasValue("presentation") ?
      content.Value<IPublishedContent>("presentation")?.Url() ?? "" : "";

      <div class="news-item">
        <div class="date">
          <a class="date-v1">@date</a>
          <a class="date-v2">@dateV2</a>
        </div>

        <div class="title">
          <p>@title</p>
        </div>

        <div class="webcast">
          @if (webcast != null && webcast.Any())
          {
            <a href="@webcast" target="_blank">
              <img src="@webcastIcon" alt="">
            </a>
          }
        </div>

        <div class="presentation">
          @if (presentation != null && presentation.Any())
          {
            <a href="@presentation" target="_blank">
              <img src="@presentationIcon" alt="">
            </a>
          }
        </div>
      </div>
    }
  </div>

</div>
