html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

a {
  text-decoration: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

// Variable colors
$color-primary: var(--color-primary);
$color-secondary: var(--color-secondary);
$color-tertiary: var(--color-tertiary);
$colorBlue: #0cb6f2;
$colorBlack: #0d1c21;
$colorGrey: rgba(13, 28, 33, 0.54);
$colorPink: #ff558c;

/** Not edit in here */
html,
body {
  font-size: 16px;
  scroll-behavior: smooth;
}

html {
  font-size: 62.5%;

  // @media screen and (max-width: 61.938rem) {
  //   font-size: 55%;
  // }

  // @media screen and (max-width: 28.125rem) {
  //   font-size: 51%;
  // }
}

// html[lang="cn"],
// html[lang="hk"] {
//   body {
//     font-family: "Microsoft YaHei UI", "Microsoft YaHei-Regular";

//     font-weight: normal;
//     color: #333333;
//     overflow-x: hidden;
//   }
// }

html[lang="en"],
html[lang="cn"],
html[lang="hk"] {
  body {
    // font-family: "Sofia Pro";
    font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Microsoft YaHei-Regular";
    font-weight: normal;
    color: #333333;
    background-color: #f6fbff;
    overflow-x: hidden;
  }
}

.font-bold-pro {
  // font-family: "Sofia Pro Bold";
}

.bold-cn-font {
  font-family: "Microsoft YaHei Bold";
}

html {
  scroll-padding-top: 13rem;

  &::-webkit-scrollbar {
    width: 0.8rem;
  }

  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 0.5rem grey;
    // border-radius: .5rem;
  }

  &::-webkit-scrollbar-thumb {
    background: $colorBlue;
    border-radius: 1rem;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: $colorPink;
  }

  @media (max-width: 800px) {
    // scroll-padding-top: 7rem;
  }
}

/******** Not edit in here *********/

.financial-information {
  .inner-content {
    @media (min-width: 200px) and (max-width: 767px) {
      padding-bottom: 12rem;
    }
  }
}

.inner-content {
  background-color: #fff;

  padding-bottom: 12rem;

  @media screen and (max-width: 75rem) {
    //1200px
    //margin: 8rem 0;
  }

  @media (min-width: 768px) and (max-width: 1280px) {
    padding-bottom: 5rem;
  }

  @media (min-width: 200px) and (max-width: 767px) {
    padding-bottom: 3rem;
  }

  @media screen and (max-width: 48rem) {
    //768px
    margin: 0;
  }

  .page-title {
    font-style: normal;
    font-weight: 400;
    font-size: 64px;
    text-align: center;
    color: #0a2333;
    margin-bottom: 60px;

    @media screen and (min-width: 1024px) and (max-width: 1470px) {
      font-size: 4rem;
    }

    @media screen and (min-width: 992px) and (max-width: 1025px) {
      font-size: 3.8rem;
    }

    @media screen and (max-width: 991px) {
      margin-bottom: 5rem;
      font-size: 3.6rem;
    }
  }
}

h3.title {
  font-style: normal;
  font-weight: 700;
  font-size: 6.4rem;
  line-height: 1;

  @media screen and (min-width: 1024px) and (max-width: 1470px) {
    font-size: 4rem;
  }

  @media screen and (min-width: 992px) and (max-width: 1023px) {
    font-size: 3.8rem;
  }

  @media screen and (max-width: 991px) {
    font-size: 2.8rem;
  }
}

.text-font-size-64 {
  font-size: 6.4rem;

  @media screen and (max-width: 991px) {
    font-size: 2.4rem;
  }
}
.text-font-size-40 {
  font-size: 4rem;

  @media screen and (max-width: 991px) {
    font-size: 2.4rem;
  }
}
.text-font-size-24 {
  font-size: 2.4rem;

  @media screen and (max-width: 991px) {
    font-size: 1.4rem !important;
  }
}

.text-font-size-20 {
  font-size: 2rem;

  @media screen and (max-width: 991px) {
    font-size: 1.4rem;
  }
}

.text-font-size-16 {
  font-size: 1.6rem;
  @media screen and (max-width: 991px) {
    font-size: 1.4rem !important;
  }
}

.text-font-size-14 {
  font-size: 1.4rem;

  @media screen and (max-width: 991px) {
    font-size: 1.2rem;
  }
}

.image-hover-effect {
  overflow: hidden;

  img {
    transition: all 0.5s ease-in-out;
  }

  &:hover {
    img {
      transform: scale(1.1);
    }
  }
}

.error-detail {
  p {
    font-size: 1.6rem;
  }
}

@mixin transition {
  transition: all 0.3s ease-in-out;
}

a:hover {
  text-decoration: none;
}

@keyframes textDance {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-3px);
  }
}

@keyframes loop {
  0%,
  15% {
    transform: translateY(-5px);
  }

  50%,
  66% {
    transform: translateY(5px);
  }

  90%,
  100% {
    transform: translateY(-5px);
  }
}

@keyframes headerFix {
  from {
    transform: translateY(-100%);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes headerFixReverse {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-100%);
  }
}

.container {
  padding: 0 15px;

  @media (min-width: 62rem) {
    //992px
    max-width: 100rem;
    width: 100%;
    margin: 0 auto;
  }

  @media (min-width: 75rem) {
    //1200px
    max-width: 118rem;
    width: 100%;
    margin: 0 auto;
  }

  @media (min-width: 87.5rem) {
    //1400px
    max-width: 130rem;
    width: 100%;
    margin: 0 auto;
  }

  @media (min-width: 95.625rem) {
    //1500px
    max-width: 143rem;
    width: 100%;
    margin: 0 auto;
  }
}

.button {
  width: 15rem;
  height: 5rem;
  background: #ff558c;
  border-radius: 3rem;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 400;
  font-size: 2rem;
  line-height: 2rem;
  color: #ffffff;
  margin: 0 auto;

  &.btn-beauty {
    &:hover {
      color: #ffffff;
    }
  }

  @media (max-width: 600px) {
    width: 10rem;
    height: 3.4rem;
    font-size: 1.4rem;
  }
}

.fade:not(.show) {
  display: none !important;
}

.wrap-slider-effect {
  position: relative;
  height: 100%;

  .link-hidden {
    position: absolute;
    // top: 65%;
    top: calc(63% - 5rem);
    left: 50%;
    width: 150px;
    height: 50px;
    //height: 100px;
    transform: translate(-50%, -50%);
    z-index: 2;
  }

  .qr-wrap {
    position: absolute;
    text-align: center;
    top: calc(63% + 2rem);
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;

    z-index: 10;

    .qr-image {
      position: absolute;
      top: 70px;
      left: 50%;
      transform: translateX(-50%);
      opacity: 0;
      visibility: hidden;

      @include transition();
    }

    @media (max-width: 1200px) {
      top: calc(63% + 6rem);
    }

    @media (max-width: 1025px) {
      display: none;
    }

    &.open-qr {
      .qr-image {
        opacity: 1;
        visibility: visible;
      }
    }
  }
}

.slider-vertical {
  max-height: 100vh;
  @include transition();

  .swiper-slide {
    overflow: hidden;
  }

  .slide-1,
  .slide-2 {
    height: 100vh !important;
  }

  .slide-3 {
    height: 50px !important;
    background-color: #fff;
  }

  .swiper-slide {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.home {
  .inner-content {
    padding: 0;
    background: transparent;
    margin-top: -1px !important;
    margin-bottom: 0 !important;
    position: relative;
    z-index: 2;
    overflow-x: hidden;
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */

    &::-webkit-scrollbar {
      display: none;
    }

    section {
      background: #fff;
    }
  }
}

.error-page {
  .inner-content {
    margin-top: 12rem;

    @media (min-width: 320px) and (max-width: 767px) {
      margin-top: 5rem;
    }
  }
}

.sitemap {
  .sitemap-container {
    margin-top: 12rem;

    @media (min-width: 320px) and (max-width: 767px) {
      margin-top: 5rem;
    }
  }
}

.display-on-pc {
  display: none;

  @media (min-width: 1025px) {
    display: block;
  }
}

.display-on-mobile {
  display: block;

  @media (min-width: 1025px) {
    display: none;
  }
}

.site-content {
  position: relative;

  .back-to-top {
    position: fixed;
    bottom: 20%;
    right: 5%;
    z-index: 9;
    cursor: pointer;
    visibility: hidden;

    &.show {
      visibility: visible;
    }
  }
}

:lang(cn),
:lang(hk) {
  body {
    .childpage-content:not(.home).esg-report {
      .banner {
        .first-line {
          width: 100%;
          line-height: 0;
          @media (min-width: 1023px) {
            width: 31%;
            line-height: 1.2;
          }
          @media (min-width: 1024px) and (max-width: 1400px) {
            width: 27%;
            line-height: 1.2;
          }
        }
      }
    }
  }
}

#event-calendar {
  min-height: 80rem;
}

.child-head {
  .d-block {
    .nav-link {
      border-bottom: unset;
      background-color: #ff558c;
      color: #ffffff !important;
    }
  }
}

.tab-content {
  .d-block {
    display: block !important;
    opacity: 1 !important;
  }
}

body.hidden-qr {
  .qr-wrap {
    display: none;
  }
}

html {
  -ms-overflow-style: -ms-autohiding-scrollbar;
}

//Firefox
body {
  overflow: -moz-scrollbars-none;
}

//Internet Explorer, Edge
body {
  -ms-overflow-style: none;
}


.tab-contents {
  display: none;
  &.active {
      display: block;
  }
}