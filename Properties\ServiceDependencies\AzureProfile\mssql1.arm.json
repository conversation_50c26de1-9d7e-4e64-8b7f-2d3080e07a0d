{"$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"resourceGroupName": {"type": "string", "defaultValue": "Default-SQL-EastAsia", "metadata": {"_parameterType": "resourceGroup", "description": "Name of the resource group for the resource. It is recommended to put resources under same resource group for better tracking."}}, "resourceGroupLocation": {"type": "string", "defaultValue": "eastasia", "metadata": {"_parameterType": "location", "description": "Location of the resource group. Resource groups could have different location than resources."}}, "resourceLocation": {"type": "string", "defaultValue": "[parameters('resourceGroupLocation')]", "metadata": {"_parameterType": "location", "description": "Location of the resource. By default use resource group's location, unless the resource provider is not supported there."}}}, "resources": [{"type": "Microsoft.Resources/resourceGroups", "name": "[parameters('resourceGroupName')]", "location": "[parameters('resourceGroupLocation')]", "apiVersion": "2019-10-01"}, {"type": "Microsoft.Resources/deployments", "name": "[concat(parameters('resourceGroupName'), 'Deployment', uniqueString(concat('umb-bilibili', subscription().subscriptionId)))]", "resourceGroup": "[parameters('resourceGroupName')]", "apiVersion": "2019-10-01", "dependsOn": ["[parameters('resourceGroupName')]"], "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "resources": [{"kind": "v12.0", "location": "[parameters('resourceLocation')]", "name": "e9fsz6tocr", "type": "Microsoft.Sql/servers", "apiVersion": "2017-10-01-preview"}, {"sku": {"name": "Basic", "tier": "Basic", "capacity": 5}, "kind": "v12.0,user", "location": "[parameters('resourceLocation')]", "name": "e9fsz6tocr/umb-bilibili", "type": "Microsoft.Sql/servers/databases", "apiVersion": "2017-10-01-preview", "dependsOn": ["e9fsz6tocr"]}]}}}], "metadata": {"_dependencyType": "mssql.azure"}}