﻿// $(document).ready(function() {
//     wow = new WOW()
//     wow.init();
// });

$(document).ready(function() {

    AOS.init({
        once: true,
    });


    // setTimeout(function() {
    //     $('.count').each(function () {

    //         $(this).prop('Counter',0).animate({
    //             Counter: $(this).text()
    //         }, {
    //             duration: 5000,
    //             easing: 'swing',
    //             step: function (now) {
    //                 $this.text(Math.celi(now));
    //             }
    //         });
    //     });
    // },2000);

    var isAlreadyRun = false;

    $(window).scroll( function() {
        // $('.count').each(function() {
        //     var $this = $(this),
        //     countTo = $this.attr('data-count').replace(/,/g, '');
          
        //     $({
        //         countNum: $this.text()
        //     }).animate({
        //         countNum: countTo
        //     }, {
        //         duration: 1000,
        //         easing: 'swing',
        //         step: function() {
        //             $this.text(numberWithCommas(Math.floor(this.countNum)));
        //         },
        //         complete: function() {
        //             $this.text(numberWithCommas(this.countNum));
        //         }
        //     });
        // });

        // isAlreadyRun = true;
          
        // function numberWithCommas(x) {
        //     return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        // }
        
        
        const counters = document.querySelectorAll('.count');
        const speed = 100;

        counters.forEach( counter => {
            const animate = () => {
                const value = +counter.getAttribute('data-count');
                const realvalue = counter.getAttribute('data');
            const data = +counter.innerText;
            
            const time = value / speed;
            if(data < value) {
                counter.innerText = Math.ceil(data + time);
                setTimeout(animate, 250);
                }else{
                counter.innerText = realvalue;
                }
            }
            animate();

            
        });

        isAlreadyRun = true;
        
    });

    var btn = $('.back-to-top');

    $(window).scroll(function() {
    if ($(window).scrollTop() > 300) {
        btn.addClass('show');
    } else {
        btn.removeClass('show');
    }
    });

    btn.on('click', function(e) {
        e.preventDefault();
        $('html, body').animate({scrollTop:0}, '300');
    });

    $('.content-tab:first-child').addClass('d-block');
    $('.device_buttons:first-child').addClass('d-block');

    $(".device_buttons").each(function () {
        $(this).on("click", function () {
            var listItems = $(".device_buttons");
    
            for (let i = 0; i < listItems.length; i++) {
                if (this != listItems[i]) {
                    listItems[i].classList.remove("d-block");
                }
            }
    
            if (this.classList.contains("d-block")) {
                this.classList.remove("d-block");
            } else if (!this.classList.contains("d-block")) {
                this.classList.add("d-block");
            }
    
            console.log($(this).data())
            var count = $(this).data('buttonCount')
            console.log('count ' + count)
    
    
            // ES5
            $('.content-tab').each(function(index, item)  {
                if (item.getAttribute('data-content-count') != count) {
                    item.classList.remove('d-block')
                }
    
                // console.log('content count ' + item.getAttribute('data-content-count'))
                if (item.getAttribute('data-content-count') == count) {
                    if (!item.classList.contains('d-block')) {
                        console.log("add?")
                        item.classList.add('d-block')
                    } else if (item.classList.contains('d-block')) {
                        console.log("remove?")
                        item.classList.remove('d-block')
                    }
                }
            })
        });
    });
});