@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;
@{
  var home = Model.Root();
  List<IPublishedContent> menuItems = (List<IPublishedContent>)ViewData["menuItems"];
  bool isFooter = false;
  bool isMobile = false;
  if (home == Model)
  {
    isFooter = (bool)ViewData["isFooter"];
  }
  isMobile = (bool)ViewData["isMobile"];
  generateMenu(menuItems, false, false, isFooter, isMobile);
}

@functions {
  public bool isHomePage(IPublishedContent menuItem)
  {
    return menuItem.ContentType.Alias == "homePage";
  }

  public bool isRedirectPage(IPublishedContent menuItem)
  {
    return (menuItem.ContentType.Alias == "redirectPage" || menuItem.ContentType.Alias == "redirectLandingPage");
  }

  public bool hasAnyActiveSubMenu(List<IPublishedContent> items)
  {
    foreach (var item in items)
    {
      if (isInCurrentPageTree(item))
      {
        return true;
      }
    }
    return false;
  }

  public bool isInCurrentPageTree(IPublishedContent menuItem)
  {
    if (isHomePage(menuItem) && !isHomePage(Model))
    {
      return false;
    }

    return Model.AncestorsOrSelf().Where(x => x.Url().Equals(menuItem.Url())).Any();
  }
  public bool hasAnyChildrenOfSuitableType(IPublishedContent menuItem)
  {
    if (isHomePage(menuItem))
    {
      return false;
    }

    return (menuItem.Children().Where(x => x.IsVisible())
    .Where(x => x.ContentType.Alias != "manufacturingItem")
    .Where(x => x.ContentType.Alias != "jobItem")
    .Where(x => x.ContentType.Alias != "recentNewsItem")
    .Where(x => x.ContentType.Alias != "productPipelinePage")
    .Where(x => x.ContentType.Alias != "recentNewsItem")
    .Any());
  }

  public void generateMenu(List<IPublishedContent> menuItems, bool isSubMenu, bool hasActiveSubMenu, bool isFooter, bool
  isMobile)
  {
    bool hasAnyChildren = false;
    var menuResponsive = isMobile == true ? "menu-responsive" : "menu-pc";
    <ul class="@menuResponsive ul-menu">
      @{
        var existing = false;
      }
      @foreach (var item in menuItems) //lv 1
      {
        //Umbraco.Cms.Core.Models.Link redirectLink = null;
        //var childItems = item.Children().Where(x => x.IsVisible());
        bool isRedirect = isRedirectPage(item);
        string href = item.Url();
        string target = "";
        hasAnyChildren = hasAnyChildrenOfSuitableType(item);
        if (isRedirect && item.HasValue("redirectLink"))
        {
          Umbraco.Cms.Core.Models.Link redirectLink = item.Value<Link>("redirectLink");
          href = redirectLink.Url;
          target = redirectLink.Target;
        }
        /*make special url for Media Categories*/
        if (item.IsDocumentType("mediaCategoryPage"))
        {
          var itemName = StrConvert(item.Name.ToString());
          href = item.Parent.Url() + "?tab=" + @itemName + "&page=1";
        }

        /* add '#' into hyperlink */
        var parent = item.Parent;
        var linkUrl = item.Url().ToString();
        var tabName = "";

        if (parent != null)
        {
          if (parent.IsDocumentType("commonTabcontentPage"))
          {
            tabName = item.Name.ToLower().Replace(" ", "-").Replace("&", "and").Replace("'", "-");
            href = parent.Url().ToString() + "#" + tabName;
          }
          if (parent.IsDocumentType("commonTabcontentPage"))
          {
            @* href = parent.Url().ToString() + "?query=" + tabName + "#" + tabName; *@
            href = parent.Url().ToString() + "?tab=" + tabName + "#" + tabName;
          }
          @* var pageSection = subItem.Name.ToLower().Replace(" ", "-").Replace("'", "-");
            var queryName = Request["query"];
            <li class="@((subItem.Name == Model.Name) || (pageSection == queryName) ? "active" : "")">
            <a href="@subItemHref" title="">@(subItem.Value<string>("pageTitle"))</a>
            </li> *@
        }

        var hassub = hasAnyChildren ? "hassub multi-level" : "";
        var activeClass = isInCurrentPageTree(item) ? "active" : "";
        var pageTitle = item.Value("pageTitle") != null ? item.Value<string>("pageTitle") : item.Value("None");
        @* if (item.Level > 2 && !existing)
          {
          <li class="menu-item back"><a href="#"><i class="iconfont icon-icon_zuo"></i>@Umbraco.GetDictionaryValue("Back")</a>
          </li>
          existing = true;
          } *@
        <li class="menu-item @hassub @activeClass <EMAIL>">
          <a href="@href" class="menu" title="" target="@target">
            @if (isMobile == true)
            {
              <span>@Html.Raw(pageTitle)</span>
            }
            else
            {
              @Html.Raw(pageTitle)
            }
          </a>

          @if (hasAnyChildren)
          {
            <div class="open-sub">
              <i class="bi bi-chevron-down"></i>
            </div>

            @if (hasAnyChildren)
            {
              hasActiveSubMenu = hasAnyActiveSubMenu(item.Children.ToList());
              generateMenu(item.Children.Where(x => x.IsVisible()).ToList(), true, hasActiveSubMenu, isFooter, isMobile)
              ;
            }
          }
        </li>
      }
    </ul>
  }
  private static string StrConvert(string name)
  {
    name = name.Replace(" ", "-");
    name = name.Replace("!", "");
    name = name.Replace("@", "");
    name = name.Replace("#", "");
    name = name.Replace("$", "");
    name = name.Replace("%", "");
    name = name.Replace("^", "");
    name = name.Replace("&", "");
    name = name.Replace("*", "");
    name = name.Replace("(", "");
    name = name.Replace(")", "");
    name = name.Replace("=", "");
    name = name.Replace("+", "");
    name = name.Replace("|", "");
    name = name.Replace("/", "");
    name = name.Replace("?", "");
    name = name.Replace(">", "");
    name = name.Replace("<", "");
    name = name.Replace(".", "");
    name = name.Replace(",", "");
    return name.ToLower();
  }
}