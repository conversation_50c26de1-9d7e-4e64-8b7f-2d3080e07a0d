﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;
@{
    var corporateGovernancePage = Model.DescendantsOfType("corporateGovernancePage")?.FirstOrDefault() ?? null;
    var pageDescription = corporateGovernancePage.Value("pageDescription").ToString();
    var membershipTableTitle = corporateGovernancePage.Value("membershipTableTitle").ToString();
    var documentsTitle = corporateGovernancePage.Value("documentsTitle").ToString();
    var documentGroups = corporateGovernancePage.Value<IEnumerable<IPublishedElement>>("documentGroups").ToList();
    var members = corporateGovernancePage.Children.Where(x => x.IsVisible()).OrderBy(x => x.SortOrder).ToList();
    List<IPublishedContent> listAuditCommittee = new List<IPublishedContent>();
    List<IPublishedContent> listCompensationCommittee = new List<IPublishedContent>();
    List<IPublishedContent> listNominatingAndCorporateCommittee = new List<IPublishedContent>();
    var chairmanIcon = "/media/slrf5zs4/chairman.png";
    var memberIcon = "/media/pcrephnl/member.png";
    var pdfIcon = "/media/0xzbdlyq/pdf-icon.png";
    var closeIcon = "/media/gqeluogr/close.png";
    var pageName = corporateGovernancePage.Name;
    var titleThirdTable = corporateGovernancePage.Value("titleThirdTable").ToString();
}
<div class="corporate-governance-container">
    <div class="page-desc">@Html.Raw(pageDescription)</div>
    <div class="committee-table">
        <div class="table-title">@membershipTableTitle</div>
        <div class="box-table">
            <table width="100%">
                <thead>
                    <tr>
                        <td></td>
                        <td>@Umbraco.GetDictionaryValue("Audit Committee")</td>
                        <td>@Umbraco.GetDictionaryValue("Compensation Committee")</td>
                        <td>@Umbraco.GetDictionaryValue("Nomination Committee")</td>
                        <td>@Umbraco.GetDictionaryValue("Corporate Governance Committee")</td>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in members)
                    {
                        bool auditCommittee = false;
                        var auditCommitteePosition = 0; /*0 - None; 1 - Chairperson; 2 - Member*/

                        bool compensationCommittee = false;
                        var compensationCommitteePosition = 0; /*0 - None; 1 - Chairperson; 2 - Member*/

                        bool nominationCommittee = false;
                        var nominationCommitteePosition = 0; /*0 - None; 1 - Chairperson; 2 - Member*/

                        bool corporateGovernanceCommittee = false;
                        var corporateGovernanceCommitteePosition = 0; /*0 - None; 1 - Chairperson; 2 - Member*/

                        var persontName = item.Value("personName").ToString();
                        var convertName = Convert(pageName + "-" + persontName);
                        var position = item.Value("position").ToString();
                        var personInformation = item.Value("personInformation").ToString();
                        var personDocuments = item.Value<IEnumerable<IPublishedElement>>("personDocuments").ToList();
                        if (!item.Value("auditCommittee").Equals("None"))
                        {
                            auditCommittee = true;
                            if (item.Value("auditCommittee").Equals("Chairperson"))
                            {
                                auditCommitteePosition = 1;
                            }
                            else
                            {
                                auditCommitteePosition = 2;
                            }
                        }
                        if (!item.Value("compensationCommittee").Equals("None"))
                        {
                            compensationCommittee = true;
                            if (item.Value("compensationCommittee").Equals("Chairperson"))
                            {
                                compensationCommitteePosition = 1;
                            }
                            else
                            {
                                compensationCommitteePosition = 2;
                            }
                        }
                        if (!item.Value("nominationCommittee").Equals("None"))
                        {
                            nominationCommittee = true;
                            if (item.Value("nominationCommittee").Equals("Chairperson"))
                            {
                                nominationCommitteePosition = 1;
                            }
                            else
                            {
                                nominationCommitteePosition = 2;
                            }
                        }
                        if (!item.Value("corporateGovernanceCommittee").Equals("None"))
                        {
                            corporateGovernanceCommittee = true;
                            if (item.Value("corporateGovernanceCommittee").Equals("Chairperson"))
                            {
                                corporateGovernanceCommitteePosition = 1;
                            }
                            else
                            {
                                corporateGovernanceCommitteePosition = 2;
                            }
                        }
                        <tr>
                            <td class="name" data-toggle="modal" data-target="@("#" + convertName)">@persontName</td>
                            <td class="position">
                                @if (auditCommittee == true)
                                {
                                    if (auditCommitteePosition == 1)
                                    {
                                        <img src="@chairmanIcon" alt="" />
                                    }
                                    else if (auditCommitteePosition == 2)
                                    {
                                        <img src="@memberIcon" alt="" />
                                    }
                                }
                            </td>
                            <td class="position">
                                @if (compensationCommittee == true)
                                {
                                    if (compensationCommitteePosition == 1)
                                    {
                                        <img src="@chairmanIcon" alt="" />
                                    }
                                    else if (compensationCommitteePosition == 2)
                                    {
                                        <img src="@memberIcon" alt="" />
                                    }
                                }
                            </td>
                            <td class="position">
                                @if (nominationCommittee == true)
                                {
                                    if (nominationCommitteePosition == 1)
                                    {
                                        <img src="@chairmanIcon" alt="" />
                                    }
                                    else if (nominationCommitteePosition == 2)
                                    {
                                        <img src="@memberIcon" alt="" />
                                    }
                                }
                            </td>
                            <td class="position">
                                @if (corporateGovernanceCommittee == true)
                                {
                                    if (corporateGovernanceCommitteePosition == 1)
                                    {
                                        <img src="@chairmanIcon" alt="" />
                                    }
                                    else if (corporateGovernanceCommitteePosition == 2)
                                    {
                                        <img src="@memberIcon" alt="" />
                                    }
                                }
                            </td>
                        </tr>
                        <!-- Modal -->
                        <div id="@convertName" class="modal fade corporate-governance-modal" role="dialog">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><img src="@closeIcon"
                                                alt="Close" /></button>
                                        <div class="head-info">
                                            <div class="name">@persontName</div>
                                            <div class="position">@position</div>
                                        </div>
                                    </div>
                                    <div class="modal-body">
                                        <div class="information">
                                            <div class="info">
                                                <div class="cg-desc">@Html.Raw(personInformation)</div>
                                                @if (personDocuments.Count() > 0)
                                                {
                                                    <div class="person-documents">
                                                        @foreach (var document in personDocuments)
                                                        {
                                                            var title = document.Value("title").ToString();
                                                            var pdfFile = "";
                                                            if (document.HasValue("file") &&
                                                            document.Value<IPublishedContent>("file") != null)
                                                            {
                                                                pdfFile = document.Value<IPublishedContent>("file").Url();
                                                            }
                                                            var documentIcon = chairmanIcon;
                                                            if (document.Value("documentType").Equals("Member"))
                                                            {
                                                                documentIcon = memberIcon;
                                                            }
                                                            <div class="documents-item">
                                                                <div class="icon"><img src="@documentIcon" alt="Type Icon" /></div>
                                                                <div class="title"><a href="@pdfFile" target="_blank"
                                                                        title="@title">@title</a></div>
                                                            </div>
                                                        }
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    }
                </tbody>
            </table>
        </div>

        <div class="icon-note">
            <div class="icon-item">
                <div class="icon"><img src="@chairmanIcon" alt="" /></div>
                <div class="text">@Umbraco.GetDictionaryValue("Chairperson") </div>
            </div>
            <div class="icon-item">
                <div class="icon"><img src="@memberIcon" alt="" /></div>
                <div class="text">@Umbraco.GetDictionaryValue("Member")</div>
            </div>
        </div>
    </div>
    <div class="documents-box">
        <div class="box-title">@documentsTitle</div>
        @if (documentGroups.Count() > 0)
        {
            <div class="box-content">
                @foreach (var group in documentGroups)
                {
                    var groupTitle = group.Value("groupTitle").ToString();
                    var files = group.Value<IEnumerable<IPublishedElement>>("files").ToList();
                    <div class="documents-group">
                        <div class="group-title text-font-size-20">@groupTitle</div>
                        <div class="document-list">
                            @foreach (var item in files)
                            {
                                var title = item.Value("title").ToString();
                                var pdfFile = "";
                                if (item.HasValue("file") && item.Value<IPublishedContent>("file") != null)
                                {
                                    pdfFile = item.Value<IPublishedContent>("file").Url();
                                }
                                <a href="@pdfFile" target="_blank" title="@title">
                                    <div class="item">
                                        <div class="icon"><img src="@pdfIcon" alt="PDF Icon" /></div>
                                        <div class="title">@title</div>
                                    </div>
                                </a>
                            }
                        </div>
                    </div>
                }
            </div>
        }
    </div>
</div>
@functions {
    public static string Convert(string name)
    {
        // string str = "";
        //equipmentName = Regex.Replace(equipmentName, @"[^A-Za-z0-9_\.~]+", "-");
        name = name.Replace(" ", "-");
        name = name.Replace("!", "");
        name = name.Replace("@", "");
        name = name.Replace("#", "");
        name = name.Replace("$", "");
        name = name.Replace("%", "");
        name = name.Replace("^", "");
        name = name.Replace("&", "");
        name = name.Replace("*", "");
        name = name.Replace("(", "");
        name = name.Replace(")", "");
        name = name.Replace("=", "");
        name = name.Replace("+", "");
        name = name.Replace("|", "");
        name = name.Replace("/", "");
        name = name.Replace("?", "");
        name = name.Replace(">", "");
        name = name.Replace("<", "");
        name = name.Replace(".", "");
        name = name.Replace(",", "");
        return name.ToLower();
    }
}