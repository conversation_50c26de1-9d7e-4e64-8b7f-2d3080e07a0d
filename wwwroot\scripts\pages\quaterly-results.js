var quarterlyResultsSwiper = new Swiper(".quarterly-results-swiper", {
    spaceBetween: 0,
    lidesPerView: 4,
    slidesPerGroup: 1,
    loop: false,
    loopFillGroupWithBlank: true,

    breakpoints: {
        0: {
            slidesPerView: 1,
        },
        767: {
            slidesPerView: 2,
        },
        1061: {
            slidesPerView: 2,
        },
        1300: {
            slidesPerView: 3,
        },
        1440: {
            slidesPerView: 3,
        },
        1660: {
            slidesPerView: 3,
        },
    },
    navigation: {
        nextEl: ".quarterly-results-swiper-next",
        prevEl: ".quarterly-results-swiper-prev",
    },
});

// var myAudio = document.getElementById("myAudio");
// var isPlaying = false;

// function togglePlay() {
//     isPlaying ? myAudio.pause() : myAudio.play();

//     myAudio.onplaying = function() {
//         isPlaying = true;
//     };
//     myAudio.onpause = function() {
//         isPlaying = false;
//     };
// };