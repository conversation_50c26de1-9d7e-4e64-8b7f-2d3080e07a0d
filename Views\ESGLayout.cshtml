﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;

@{
	Layout = "ChildPageLayout.cshtml";
	//ESG Banner
	var eSGBanner = Model.Value<IPublishedContent>("eSGBanner")?.Url() ?? "";
	var eSGTitle = Model.Value("eSGTitle");
	var eSGContent = Model.Value("eSGContent");
	var eSGSign = Model.Value("eSGSign");

	//ESG Reports
	var eSGReportPhoto = Model.Value<IPublishedContent>("eSGReportPhoto")?.Url() ?? "";
	var eSGReportTitle = Model.Value("eSGReportTitle");
	var eSGReportIntro = Model.Value("eSGReportIntro");
	var eSGReportLinkToReportPage = (Model.HasValue("eSGReportLinkToReportPage") &&
	Model.Value<Link>("eSGReportLinkToReportPage") != null) ?
	Model.Value<Link>("eSGReportLinkToReportPage").Url : "";

	//Honors and Awards
	var honorsAndAwardsBackground = Model.Value<IPublishedContent>("honorsAndAwardsBackground")?.Url() ?? "";
	var honorsAndAwardsSectionTitle = Model.Value("honorsAndAwardsTitle");
	var honorsAndAwardsList = Model.Value<IEnumerable<IPublishedElement>>("honorsAndAwardsList").ToList();

	//ESG Ratings
	var eSGRatingsTitle = Model.Value("eSGRatingsTitle");
	var eSGRatingsTitleIntro = Model.Value("eSGRatingsTitleIntro");
	var eSGRatingsContent = Model.Value("eSGRatingsContent");
	var eSGRatingsPhoto = Model.Value<IPublishedContent>("eSGRatingsPhoto")?.Url() ?? "";

	//Bilibili Public Welfare
	var bilibiliPublicWelfareTitle = Model.Value("bilibiliPublicWelfareTitle");
	var bilibiliPublicWelfarePublishedDate = Model.Value("bilibiliPublicWelfarePublishedDate");
	var bilibiliPublicWelfareContent = Model.Value("bilibiliPublicWelfareContent");
	var bilibiliPublicWelfareItem = Model.Value<IEnumerable<IPublishedElement>>("bilibiliPublicWelfareItem").ToList();
	var bilibiliPublicWelfareLink = (Model.HasValue("bilibiliPublicWelfareLink") &&
	Model.Value<Link>("bilibiliPublicWelfareLink") != null) ?
	Model.Value<Link>("bilibiliPublicWelfareLink").Url : "";
}

<div class="esg-container">
	@* Section banner *@
	<div class="esg-banner" style="background-image: url('@eSGBanner')">
		<div class="container">
			<div class="esg__wrapper" data-aos="fade-right" data-aos-duration="1000">
				<h3 class="esg__title title">
					@eSGTitle
				</h3>
				<div class="esg__content text-font-size-24">
					@eSGContent
				</div>
				<div class="esg__sign">
					@eSGSign
				</div>
			</div>
		</div>
	</div>

	@* Esg Reports *@
	<div class="esg-reports container">
		<h3 class="title esg-reports__title">
			@eSGReportTitle
		</h3>
		<div class="esg-reports__wrapper">
			<div class="esg-reports__photo">
				<img src="@eSGReportPhoto" alt="@eSGReportTitle">
			</div>
			<div class="esg-reports__content text-font-size-24">
				@eSGReportIntro
				<a href="@eSGReportLinkToReportPage" target="_self" class="button esg-reports__view-more">
					@Umbraco.GetDictionaryValue("View More")
				</a>
			</div>
		</div>
	</div>

	@* Honors and Awards *@
	<div class="esg__honors-and-awards" style="background-image: url('@honorsAndAwardsBackground')">
		<div class="container">
			<h3 class="title esg-haa__title">@honorsAndAwardsSectionTitle
			</h3>
			<div class="esg-haa__list">
				@if (honorsAndAwardsList != null && honorsAndAwardsList.Any())
				{
					foreach (var item in honorsAndAwardsList)
					{
						var honorsAndAwardsSign = item.Value("honorsAndAwardsSign");
						var honorsAndAwardsTitle = item.Value<String>("honorsAndAwardsTitle");
						var honorsAndAwardsIcon = item.Value<IPublishedContent>("honorsAndAwardsIcon")?.Url() ?? "";
						<div class="esg-haa__item">
							<div class="esg-haa__item-icon">
								<img src="@honorsAndAwardsIcon" alt="">
							</div>
							<div class="esg-haa__item-title text-font-size-20">
								@Html.Raw(honorsAndAwardsTitle)
							</div>
							<div class="esg-haa__item-sign text-font-size-14">
								@honorsAndAwardsSign
							</div>
						</div>
					}
				}
			</div>
		</div>
	</div>

	@* Esg Ratings *@
	<div class="esg-ratings-container container">
		<h3 class="title esg-ratings__title">@eSGRatingsTitle</h3>
		<div class="esg-ratings__wrapper">
			<div class="esg-ratings__photo">
				<img src="@eSGRatingsPhoto" alt="@eSGRatingsTitle">
			</div>
			<div class="esg-ratings__content">
				<div class="esg-ratings__title-intro ">
					@eSGRatingsTitleIntro
				</div>
				<div class="esg-ratings__intro text-font-size-16">@eSGRatingsContent</div>
			</div>
		</div>
	</div>

	@* Bilibili Public Welfare *@
	<div class="bilibili-public-welfare-container">
		<div class="container">
			<h3 class="title bpw__title">@bilibiliPublicWelfareTitle</h3>
			<div class="bpw__content">
				@bilibiliPublicWelfareContent
			</div>
			<div class="bpw__list">
				@if (bilibiliPublicWelfareItem != null && bilibiliPublicWelfareItem.Any())
				{
					foreach (var item in bilibiliPublicWelfareItem)
					{
						var numberCount = item.Value("numberCount");
						var contentCount = item.Value("contentCount");

						<div class="item">
							<div class="box-count">
								<div class="number-count text-font-size-64">
									@Html.Raw(numberCount)
								</div>
							</div>
							<div class="content-count text-font-size-24">
								@contentCount
							</div>
						</div>
					}
				}
			</div>
			<a href="@bilibiliPublicWelfareLink" target="_blank" class="button bpw__view-more">
				@Umbraco.GetDictionaryValue("View More")
			</a>
			<div class="bpw__date text-font-size-14 ">
				@bilibiliPublicWelfarePublishedDate
			</div>
		</div>
	</div>

	@* Quick links *@
	<section class="hp__quicklinks">
		@{
			Html.RenderPartial("~/Views/Partials/HomePage/hp-quicklinks.cshtml");
		}
	</section>


</div>